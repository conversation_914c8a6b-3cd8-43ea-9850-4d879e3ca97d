# Go pprof 性能分析指南

## 概述

本项目已集成Go的pprof性能分析工具，可以帮助你分析程序的CPU使用、内存分配、goroutine状态等性能指标。

## 配置说明

pprof HTTP服务器会在服务启动时自动启动，端口配置在 `cmd/config.yml` 中：

```yaml
http_port: 21401  # pprof服务端口
```

## 访问方式

### 1. Web界面（推荐）

直接在浏览器中访问：
```
http://localhost:21401/debug/pprof/
```

### 2. 命令行工具

```bash
# CPU性能分析（采样30秒）
go tool pprof http://localhost:21401/debug/pprof/profile?seconds=30

# 堆内存分析
go tool pprof http://localhost:21401/debug/pprof/heap

# goroutine分析
go tool pprof http://localhost:21401/debug/pprof/goroutine

# 阻塞分析
go tool pprof http://localhost:21401/debug/pprof/block

# 互斥锁分析
go tool pprof http://localhost:21401/debug/pprof/mutex
```

## 常用分析场景

### 1. CPU性能分析

```bash
# 采样30秒的CPU使用情况
go tool pprof http://localhost:21401/debug/pprof/profile?seconds=30

# 进入交互模式后常用命令：
# top10        - 显示CPU使用最多的10个函数
# list 函数名   - 显示函数的详细代码
# web          - 生成调用图（需要安装graphviz）
# png          - 生成PNG格式的调用图
# quit         - 退出
```

### 2. 内存分析

```bash
# 分析堆内存使用
go tool pprof http://localhost:21401/debug/pprof/heap

# 常用命令：
# top10        - 显示内存使用最多的10个函数
# top10 -cum   - 按累计内存使用排序
# list 函数名   - 显示函数的内存分配详情
```

### 3. goroutine分析

```bash
# 分析goroutine状态
go tool pprof http://localhost:21401/debug/pprof/goroutine

# 查看goroutine数量和状态
curl http://localhost:21401/debug/pprof/goroutine?debug=1
```

### 4. 火焰图生成

```bash
# 生成CPU火焰图（推荐）
go tool pprof -http=:8081 http://localhost:21401/debug/pprof/profile?seconds=30

# 生成内存火焰图
go tool pprof -http=:8081 http://localhost:21401/debug/pprof/heap
```

## 性能优化建议

### 1. CPU优化
- 关注 `top` 命令显示的高CPU使用函数
- 使用 `list` 命令查看具体代码行
- 优化热点代码路径

### 2. 内存优化
- 关注内存分配频繁的函数
- 检查是否有内存泄漏
- 优化数据结构和算法

### 3. goroutine优化
- 监控goroutine数量是否异常增长
- 检查是否有goroutine泄漏
- 优化并发控制

## 测试脚本

运行测试脚本验证pprof是否正常工作：

```bash
./test_pprof.sh
```

## 注意事项

1. **生产环境安全**：pprof会暴露程序内部信息，生产环境建议：
   - 限制访问IP
   - 使用认证机制
   - 或者只在需要时临时开启

2. **性能影响**：
   - CPU profiling会有轻微性能影响
   - 建议在低峰期进行分析

3. **工具依赖**：
   - 生成图形需要安装 `graphviz`
   - 火焰图需要较新版本的Go工具链

## 故障排除

### 1. 无法访问pprof
- 检查服务是否正常启动
- 确认端口配置正确
- 查看日志中的pprof启动信息

### 2. 命令行工具报错
- 确保Go工具链版本兼容
- 检查网络连接
- 验证URL地址正确

### 3. 图形生成失败
- 安装graphviz：`brew install graphviz` (macOS)
- 或使用在线火焰图：`-http` 参数
