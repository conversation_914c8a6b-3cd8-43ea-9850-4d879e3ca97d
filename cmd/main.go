package main

import (
	"context"
	"net/http"
	"runtime"
	"strconv"
	"usersrv/internal/proc"

	"usersrv/internal/server/rpc"

	_ "net/http/pprof" // 导入pprof，自动注册调试接口

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type userService struct {
	Name string
	Ctx  context.Context
}

func (u *userService) Init() error {
	u.Ctx = context.Background()
	u.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infof(u.Name + "服务Init")

	// 启动pprof HTTP服务器
	go u.startPprofServer()

	// 初始化RPC Service
	rpc.InitUserRpc()
	proc.InitGmRpc()

	return nil
}

// startPprofServer 启动pprof HTTP服务器
func (u *userService) startPprofServer() {

	addr := ":" + strconv.Itoa(8080)
	logrus.Infof("启动pprof HTTP服务器，地址: %s", addr)
	logrus.Infof("pprof调试地址: http://localhost%s/debug/pprof/", addr)

	if err := http.ListenAndServe(addr, nil); err != nil {
		logrus.Errorf("pprof HTTP服务器启动失败: %v", err)
	}
}

func (u *userService) Start() error {
	return nil
}

func (u *userService) Stop() error {
	return nil
}

func (u *userService) ForceStop() error {
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()
	driver.Run(&userService{})
}
