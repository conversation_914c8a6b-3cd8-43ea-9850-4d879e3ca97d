<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="530" onload="init(evt)" viewBox="0 0 1200 530" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#e0e0ff" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="530.0" fill="url(#background)"  />
<text text-anchor="middle" x="600" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="" x="10" y="513" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<g class="func_g" onmouseover="s('read_tsc (285 samples, 0.06%)')" onmouseout="c()">
<title>read_tsc (285 samples, 0.06%)</title><rect x="341.2" y="257" width="0.8" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (64 samples, 0.01%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (64 samples, 0.01%)</title><rect x="591.0" y="321" width="0.2" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (1,059 samples, 0.24%)')" onmouseout="c()">
<title>hrtimer_interrupt (1,059 samples, 0.24%)</title><rect x="1173.5" y="337" width="2.8" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_of (50 samples, 0.01%)')" onmouseout="c()">
<title>task_of (50 samples, 0.01%)</title><rect x="631.5" y="241" width="0.1" height="15.0" fill="rgb(226,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_save_host_state (618 samples, 0.14%)')" onmouseout="c()">
<title>vmx_save_host_state (618 samples, 0.14%)</title><rect x="938.4" y="353" width="1.7" height="15.0" fill="rgb(229,161,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_idle (105 samples, 0.02%)')" onmouseout="c()">
<title>pick_next_task_idle (105 samples, 0.02%)</title><rect x="671.1" y="305" width="0.3" height="15.0" fill="rgb(240,58,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (41 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (41 samples, 0.01%)</title><rect x="982.9" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (140 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock (140 samples, 0.03%)</title><rect x="1031.0" y="369" width="0.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_emulate_halt (74 samples, 0.02%)')" onmouseout="c()">
<title>kvm_emulate_halt (74 samples, 0.02%)</title><rect x="824.1" y="321" width="0.2" height="15.0" fill="rgb(242,123,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('delayed_work_timer_fn (101 samples, 0.02%)')" onmouseout="c()">
<title>delayed_work_timer_fn (101 samples, 0.02%)</title><rect x="400.8" y="241" width="0.3" height="15.0" fill="rgb(222,94,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (119 samples, 0.03%)')" onmouseout="c()">
<title>enqueue_hrtimer (119 samples, 0.03%)</title><rect x="293.7" y="305" width="0.3" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_bh_qs (62 samples, 0.01%)')" onmouseout="c()">
<title>rcu_bh_qs (62 samples, 0.01%)</title><rect x="402.7" y="273" width="0.1" height="15.0" fill="rgb(238,81,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (73 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (73 samples, 0.02%)</title><rect x="1166.2" y="369" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (42 samples, 0.01%)')" onmouseout="c()">
<title>update_shares (42 samples, 0.01%)</title><rect x="1127.5" y="289" width="0.2" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('render_sigset_t (46 samples, 0.01%)')" onmouseout="c()">
<title>render_sigset_t (46 samples, 0.01%)</title><rect x="972.6" y="353" width="0.2" height="15.0" fill="rgb(236,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_get_msr (2,255 samples, 0.51%)')" onmouseout="c()">
<title>vmx_get_msr (2,255 samples, 0.51%)</title><rect x="739.4" y="337" width="6.0" height="15.0" fill="rgb(217,85,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (5,101 samples, 1.15%)')" onmouseout="c()">
<title>hrtimer_start (5,101 samples, 1.15%)</title><rect x="1154.4" y="417" width="13.5" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (363 samples, 0.08%)')" onmouseout="c()">
<title>lapic_is_periodic (363 samples, 0.08%)</title><rect x="129.3" y="273" width="1.0" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___munmap (69 samples, 0.02%)')" onmouseout="c()">
<title>__GI___munmap (69 samples, 0.02%)</title><rect x="973.4" y="465" width="0.1" height="15.0" fill="rgb(206,229,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_ioctl (348,207 samples, 78.43%)')" onmouseout="c()">
<title>sys_ioctl (348,207 samples, 78.43%)</title><rect x="16.2" y="417" width="925.4" height="15.0" fill="rgb(225,38,48)" rx="2" ry="2" />
<text text-anchor="" x="19.17394731804" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sys_ioctl</text>
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (55 samples, 0.01%)')" onmouseout="c()">
<title>clockevents_program_event (55 samples, 0.01%)</title><rect x="1188.9" y="273" width="0.2" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (157 samples, 0.04%)')" onmouseout="c()">
<title>apic_update_ppr (157 samples, 0.04%)</title><rect x="512.7" y="305" width="0.4" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_to_user (102 samples, 0.02%)')" onmouseout="c()">
<title>copy_to_user (102 samples, 0.02%)</title><rect x="690.5" y="321" width="0.2" height="15.0" fill="rgb(206,70,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_futex (56 samples, 0.01%)')" onmouseout="c()">
<title>do_futex (56 samples, 0.01%)</title><rect x="983.7" y="417" width="0.1" height="15.0" fill="rgb(223,180,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('raise_softirq (710 samples, 0.16%)')" onmouseout="c()">
<title>raise_softirq (710 samples, 0.16%)</title><rect x="176.7" y="241" width="1.9" height="15.0" fill="rgb(249,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_page (100 samples, 0.02%)')" onmouseout="c()">
<title>kvm_write_guest_page (100 samples, 0.02%)</title><rect x="696.3" y="337" width="0.2" height="15.0" fill="rgb(245,123,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (138 samples, 0.03%)')" onmouseout="c()">
<title>enqueue_entity (138 samples, 0.03%)</title><rect x="1002.9" y="225" width="0.4" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (442 samples, 0.10%)')" onmouseout="c()">
<title>rebalance_domains (442 samples, 0.10%)</title><rect x="391.5" y="241" width="1.2" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_external_interrupt (761 samples, 0.17%)')" onmouseout="c()">
<title>handle_external_interrupt (761 samples, 0.17%)</title><rect x="498.1" y="337" width="2.0" height="15.0" fill="rgb(218,128,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (259 samples, 0.06%)')" onmouseout="c()">
<title>rcu_irq_exit (259 samples, 0.06%)</title><rect x="1044.4" y="369" width="0.7" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (10,485 samples, 2.36%)')" onmouseout="c()">
<title>default_wake_function (10,485 samples, 2.36%)</title><rect x="1000.2" y="289" width="27.9" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
<text text-anchor="" x="1003.23683232542" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s('kvm_emulate_halt (62 samples, 0.01%)')" onmouseout="c()">
<title>kvm_emulate_halt (62 samples, 0.01%)</title><rect x="765.9" y="305" width="0.1" height="15.0" fill="rgb(242,123,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_timer (1,061 samples, 0.24%)')" onmouseout="c()">
<title>do_timer (1,061 samples, 0.24%)</title><rect x="151.0" y="257" width="2.8" height="15.0" fill="rgb(223,101,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getnstimeofday (141 samples, 0.03%)')" onmouseout="c()">
<title>getnstimeofday (141 samples, 0.03%)</title><rect x="1098.1" y="401" width="0.4" height="15.0" fill="rgb(214,199,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (662 samples, 0.15%)')" onmouseout="c()">
<title>__wake_up (662 samples, 0.15%)</title><rect x="1174.0" y="289" width="1.8" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (353 samples, 0.08%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (353 samples, 0.08%)</title><rect x="608.6" y="305" width="0.9" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (73 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (73 samples, 0.02%)</title><rect x="1169.9" y="385" width="0.2" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_enable (94 samples, 0.02%)')" onmouseout="c()">
<title>perf_pmu_enable (94 samples, 0.02%)</title><rect x="1046.5" y="305" width="0.2" height="15.0" fill="rgb(238,51,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (374 samples, 0.08%)')" onmouseout="c()">
<title>copy_user_generic_string (374 samples, 0.08%)</title><rect x="572.9" y="305" width="1.0" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scsi_finish_command (54 samples, 0.01%)')" onmouseout="c()">
<title>scsi_finish_command (54 samples, 0.01%)</title><rect x="914.5" y="225" width="0.2" height="15.0" fill="rgb(0,215,63)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__cond_resched (62 samples, 0.01%)')" onmouseout="c()">
<title>__cond_resched (62 samples, 0.01%)</title><rect x="598.9" y="305" width="0.2" height="15.0" fill="rgb(234,125,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_run (579 samples, 0.13%)')" onmouseout="c()">
<title>vmx_vcpu_run (579 samples, 0.13%)</title><rect x="940.1" y="353" width="1.5" height="15.0" fill="rgb(235,49,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_get_interrupt (3,339 samples, 0.75%)')" onmouseout="c()">
<title>kvm_cpu_get_interrupt (3,339 samples, 0.75%)</title><rect x="505.6" y="337" width="8.8" height="15.0" fill="rgb(215,172,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (47 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (47 samples, 0.01%)</title><rect x="805.0" y="193" width="0.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_oneshot_broadcast (72 samples, 0.02%)')" onmouseout="c()">
<title>tick_check_oneshot_broadcast (72 samples, 0.02%)</title><rect x="1042.9" y="369" width="0.2" height="15.0" fill="rgb(232,124,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (78 samples, 0.02%)')" onmouseout="c()">
<title>update_shares (78 samples, 0.02%)</title><rect x="1184.8" y="225" width="0.2" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_emulate_pio (87 samples, 0.02%)')" onmouseout="c()">
<title>kvm_emulate_pio (87 samples, 0.02%)</title><rect x="766.7" y="305" width="0.2" height="15.0" fill="rgb(0,215,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_put (139 samples, 0.03%)')" onmouseout="c()">
<title>vcpu_put (139 samples, 0.03%)</title><rect x="731.4" y="337" width="0.3" height="15.0" fill="rgb(238,142,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (117 samples, 0.03%)')" onmouseout="c()">
<title>pit_has_pending_timer (117 samples, 0.03%)</title><rect x="610.3" y="305" width="0.3" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gup_pud_range (100 samples, 0.02%)')" onmouseout="c()">
<title>gup_pud_range (100 samples, 0.02%)</title><rect x="763.3" y="225" width="0.3" height="15.0" fill="rgb(0,196,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (2,187 samples, 0.49%)')" onmouseout="c()">
<title>__remove_hrtimer (2,187 samples, 0.49%)</title><rect x="1130.2" y="385" width="5.8" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (4,153 samples, 0.94%)')" onmouseout="c()">
<title>schedule (4,153 samples, 0.94%)</title><rect x="1113.9" y="433" width="11.0" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (99 samples, 0.02%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (99 samples, 0.02%)</title><rect x="137.3" y="289" width="0.2" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_timer (287 samples, 0.06%)')" onmouseout="c()">
<title>do_timer (287 samples, 0.06%)</title><rect x="1040.6" y="337" width="0.7" height="15.0" fill="rgb(223,101,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_shared_msr (177 samples, 0.04%)')" onmouseout="c()">
<title>kvm_set_shared_msr (177 samples, 0.04%)</title><rect x="599.1" y="337" width="0.5" height="15.0" fill="rgb(252,70,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task (103 samples, 0.02%)')" onmouseout="c()">
<title>dequeue_task (103 samples, 0.02%)</title><rect x="650.5" y="305" width="0.3" height="15.0" fill="rgb(236,71,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_smp_call_function_single_interrupt (107 samples, 0.02%)')" onmouseout="c()">
<title>generic_smp_call_function_single_interrupt (107 samples, 0.02%)</title><rect x="1046.4" y="385" width="0.3" height="15.0" fill="rgb(210,50,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('child_rip (1,282 samples, 0.29%)')" onmouseout="c()">
<title>child_rip (1,282 samples, 0.29%)</title><rect x="977.1" y="465" width="3.4" height="15.0" fill="rgb(211,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpu_idle (70,353 samples, 15.85%)')" onmouseout="c()">
<title>cpu_idle (70,353 samples, 15.85%)</title><rect x="983.9" y="449" width="186.9" height="15.0" fill="rgb(233,201,15)" rx="2" ry="2" />
<text text-anchor="" x="986.857134813113" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpu_idle</text>
</g>
<g class="func_g" onmouseover="s('_spin_lock (939 samples, 0.21%)')" onmouseout="c()">
<title>_spin_lock (939 samples, 0.21%)</title><rect x="291.2" y="305" width="2.5" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (366 samples, 0.08%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (366 samples, 0.08%)</title><rect x="1136.1" y="385" width="1.0" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (82 samples, 0.02%)')" onmouseout="c()">
<title>lock_hrtimer_base (82 samples, 0.02%)</title><rect x="1138.1" y="401" width="0.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (386 samples, 0.09%)')" onmouseout="c()">
<title>enqueue_task_fair (386 samples, 0.09%)</title><rect x="1174.2" y="177" width="1.1" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ntp_tick_length (45 samples, 0.01%)')" onmouseout="c()">
<title>ntp_tick_length (45 samples, 0.01%)</title><rect x="1041.0" y="321" width="0.2" height="15.0" fill="rgb(212,167,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (22,410 samples, 5.05%)')" onmouseout="c()">
<title>thread_return (22,410 samples, 5.05%)</title><rect x="620.9" y="321" width="59.6" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
<text text-anchor="" x="623.936405509195" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >thread..</text>
</g>
<g class="func_g" onmouseover="s('kvm_read_guest (134 samples, 0.03%)')" onmouseout="c()">
<title>kvm_read_guest (134 samples, 0.03%)</title><rect x="908.8" y="353" width="0.3" height="15.0" fill="rgb(246,140,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (216 samples, 0.05%)')" onmouseout="c()">
<title>__remove_hrtimer (216 samples, 0.05%)</title><rect x="1188.7" y="337" width="0.6" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_block (112 samples, 0.03%)')" onmouseout="c()">
<title>kvm_vcpu_block (112 samples, 0.03%)</title><rect x="909.1" y="353" width="0.3" height="15.0" fill="rgb(221,155,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_msr (19,182 samples, 4.32%)')" onmouseout="c()">
<title>vmx_set_msr (19,182 samples, 4.32%)</title><rect x="773.1" y="305" width="51.0" height="15.0" fill="rgb(249,17,2)" rx="2" ry="2" />
<text text-anchor="" x="776.129655281147" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vmx_s..</text>
</g>
<g class="func_g" onmouseover="s('do_readv_writev (41 samples, 0.01%)')" onmouseout="c()">
<title>do_readv_writev (41 samples, 0.01%)</title><rect x="982.9" y="401" width="0.1" height="15.0" fill="rgb(0,235,91)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (62 samples, 0.01%)')" onmouseout="c()">
<title>native_apic_mem_write (62 samples, 0.01%)</title><rect x="336.9" y="241" width="0.2" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_runnable (216 samples, 0.05%)')" onmouseout="c()">
<title>kvm_arch_vcpu_runnable (216 samples, 0.05%)</title><rect x="505.0" y="337" width="0.6" height="15.0" fill="rgb(252,200,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (102 samples, 0.02%)')" onmouseout="c()">
<title>enqueue_task (102 samples, 0.02%)</title><rect x="1023.5" y="257" width="0.3" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('msecs_to_jiffies (77 samples, 0.02%)')" onmouseout="c()">
<title>msecs_to_jiffies (77 samples, 0.02%)</title><rect x="611.1" y="321" width="0.2" height="15.0" fill="rgb(231,49,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (1,554 samples, 0.35%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (1,554 samples, 0.35%)</title><rect x="1156.9" y="369" width="4.1" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('drain_array (165 samples, 0.04%)')" onmouseout="c()">
<title>drain_array (165 samples, 0.04%)</title><rect x="979.6" y="401" width="0.4" height="15.0" fill="rgb(218,31,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (51 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_forward (51 samples, 0.01%)</title><rect x="1138.3" y="417" width="0.2" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (19,385 samples, 4.37%)')" onmouseout="c()">
<title>do_softirq (19,385 samples, 4.37%)</title><rect x="354.7" y="305" width="51.5" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
<text text-anchor="" x="357.70187055869" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_so..</text>
</g>
<g class="func_g" onmouseover="s('find_first_bit (130 samples, 0.03%)')" onmouseout="c()">
<title>find_first_bit (130 samples, 0.03%)</title><rect x="667.7" y="289" width="0.4" height="15.0" fill="rgb(218,191,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpuidle_idle_call (175 samples, 0.04%)')" onmouseout="c()">
<title>cpuidle_idle_call (175 samples, 0.04%)</title><rect x="1170.8" y="449" width="0.5" height="15.0" fill="rgb(214,193,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_IO_vfscanf (89 samples, 0.02%)')" onmouseout="c()">
<title>_IO_vfscanf (89 samples, 0.02%)</title><rect x="971.4" y="449" width="0.2" height="15.0" fill="rgb(209,15,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_reschedule_interrupt (253 samples, 0.06%)')" onmouseout="c()">
<title>smp_reschedule_interrupt (253 samples, 0.06%)</title><rect x="1184.4" y="353" width="0.7" height="15.0" fill="rgb(236,201,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (96 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (96 samples, 0.02%)</title><rect x="1153.1" y="401" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (205 samples, 0.05%)')" onmouseout="c()">
<title>irq_exit (205 samples, 0.05%)</title><rect x="1127.2" y="385" width="0.5" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (40 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (40 samples, 0.01%)</title><rect x="758.5" y="161" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (399 samples, 0.09%)')" onmouseout="c()">
<title>sched_clock_cpu (399 samples, 0.09%)</title><rect x="679.4" y="273" width="1.1" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpu_idle (6,407 samples, 1.44%)')" onmouseout="c()">
<title>cpu_idle (6,407 samples, 1.44%)</title><rect x="1172.9" y="401" width="17.0" height="15.0" fill="rgb(233,201,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('this_cpu_load (50 samples, 0.01%)')" onmouseout="c()">
<title>this_cpu_load (50 samples, 0.01%)</title><rect x="1105.4" y="401" width="0.2" height="15.0" fill="rgb(228,193,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (254 samples, 0.06%)')" onmouseout="c()">
<title>_spin_lock_irqsave (254 samples, 0.06%)</title><rect x="788.5" y="209" width="0.7" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__vmx_load_host_state (935 samples, 0.21%)')" onmouseout="c()">
<title>__vmx_load_host_state (935 samples, 0.21%)</title><rect x="685.6" y="273" width="2.5" height="15.0" fill="rgb(238,211,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (78 samples, 0.02%)')" onmouseout="c()">
<title>try_to_wake_up (78 samples, 0.02%)</title><rect x="400.8" y="129" width="0.2" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (788 samples, 0.18%)')" onmouseout="c()">
<title>native_apic_mem_write (788 samples, 0.18%)</title><rect x="337.1" y="257" width="2.1" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___libc_close (46 samples, 0.01%)')" onmouseout="c()">
<title>__GI___libc_close (46 samples, 0.01%)</title><rect x="971.8" y="465" width="0.1" height="15.0" fill="rgb(236,37,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (60 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (60 samples, 0.01%)</title><rect x="1028.6" y="321" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (6,025 samples, 1.36%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (6,025 samples, 1.36%)</title><rect x="800.5" y="209" width="16.0" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (422 samples, 0.10%)')" onmouseout="c()">
<title>vmcs_writel (422 samples, 0.10%)</title><rect x="728.1" y="305" width="1.2" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_printf (43 samples, 0.01%)')" onmouseout="c()">
<title>seq_printf (43 samples, 0.01%)</title><rect x="972.6" y="337" width="0.2" height="15.0" fill="rgb(218,83,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (80 samples, 0.02%)')" onmouseout="c()">
<title>schedule (80 samples, 0.02%)</title><rect x="767.4" y="273" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (113 samples, 0.03%)')" onmouseout="c()">
<title>native_sched_clock (113 samples, 0.03%)</title><rect x="1021.8" y="177" width="0.3" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_enable_all (93 samples, 0.02%)')" onmouseout="c()">
<title>intel_pmu_enable_all (93 samples, 0.02%)</title><rect x="1046.5" y="257" width="0.2" height="15.0" fill="rgb(252,128,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_io (88 samples, 0.02%)')" onmouseout="c()">
<title>handle_io (88 samples, 0.02%)</title><rect x="766.7" y="321" width="0.2" height="15.0" fill="rgb(0,191,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_dyntick (40 samples, 0.01%)')" onmouseout="c()">
<title>rcu_process_dyntick (40 samples, 0.01%)</title><rect x="382.4" y="209" width="0.1" height="15.0" fill="rgb(0,222,63)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_io_bus_write (82 samples, 0.02%)')" onmouseout="c()">
<title>kvm_io_bus_write (82 samples, 0.02%)</title><rect x="766.7" y="289" width="0.2" height="15.0" fill="rgb(0,230,165)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (65 samples, 0.01%)')" onmouseout="c()">
<title>_cond_resched (65 samples, 0.01%)</title><rect x="598.9" y="321" width="0.2" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (782 samples, 0.18%)')" onmouseout="c()">
<title>__run_hrtimer (782 samples, 0.18%)</title><rect x="1173.8" y="321" width="2.1" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (162 samples, 0.04%)')" onmouseout="c()">
<title>read_tsc (162 samples, 0.04%)</title><rect x="1099.9" y="385" width="0.4" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_put_guest_fpu (96 samples, 0.02%)')" onmouseout="c()">
<title>kvm_put_guest_fpu (96 samples, 0.02%)</title><rect x="684.8" y="289" width="0.3" height="15.0" fill="rgb(214,10,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (52 samples, 0.01%)')" onmouseout="c()">
<title>update_curr (52 samples, 0.01%)</title><rect x="1175.0" y="145" width="0.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (224 samples, 0.05%)')" onmouseout="c()">
<title>find_busiest_group (224 samples, 0.05%)</title><rect x="1107.1" y="273" width="0.6" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_idle (2,606 samples, 0.59%)')" onmouseout="c()">
<title>intel_idle (2,606 samples, 0.59%)</title><rect x="1177.1" y="369" width="6.9" height="15.0" fill="rgb(216,81,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_reschedule_interrupt (787 samples, 0.18%)')" onmouseout="c()">
<title>smp_reschedule_interrupt (787 samples, 0.18%)</title><rect x="1106.7" y="401" width="2.1" height="15.0" fill="rgb(236,201,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (92 samples, 0.02%)')" onmouseout="c()">
<title>default_wake_function (92 samples, 0.02%)</title><rect x="1028.4" y="305" width="0.2" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (63 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (63 samples, 0.01%)</title><rect x="1099.7" y="353" width="0.2" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_nhm_enable_all (814 samples, 0.18%)')" onmouseout="c()">
<title>intel_pmu_nhm_enable_all (814 samples, 0.18%)</title><rect x="211.1" y="177" width="2.1" height="15.0" fill="rgb(208,11,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mmu_set_spte.clone.0 (313 samples, 0.07%)')" onmouseout="c()">
<title>mmu_set_spte.clone.0 (313 samples, 0.07%)</title><rect x="754.9" y="257" width="0.8" height="15.0" fill="rgb(0,237,190)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (42 samples, 0.01%)')" onmouseout="c()">
<title>thread_return (42 samples, 0.01%)</title><rect x="983.7" y="369" width="0.1" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_queue (303 samples, 0.07%)')" onmouseout="c()">
<title>find_busiest_queue (303 samples, 0.07%)</title><rect x="668.9" y="305" width="0.8" height="15.0" fill="rgb(251,226,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (96 samples, 0.02%)')" onmouseout="c()">
<title>gfn_to_hva (96 samples, 0.02%)</title><rect x="595.2" y="321" width="0.3" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (102 samples, 0.02%)')" onmouseout="c()">
<title>try_to_wake_up (102 samples, 0.02%)</title><rect x="1028.1" y="289" width="0.3" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_callbacks (8,950 samples, 2.02%)')" onmouseout="c()">
<title>rcu_process_callbacks (8,950 samples, 2.02%)</title><rect x="367.7" y="257" width="23.8" height="15.0" fill="rgb(209,24,23)" rx="2" ry="2" />
<text text-anchor="" x="370.692940076804" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >r..</text>
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (193 samples, 0.04%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (193 samples, 0.04%)</title><rect x="689.5" y="321" width="0.5" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('resched_task (131 samples, 0.03%)')" onmouseout="c()">
<title>resched_task (131 samples, 0.03%)</title><rect x="1023.2" y="241" width="0.3" height="15.0" fill="rgb(236,7,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_needs_cpu (98 samples, 0.02%)')" onmouseout="c()">
<title>printk_needs_cpu (98 samples, 0.02%)</title><rect x="1168.5" y="417" width="0.2" height="15.0" fill="rgb(225,165,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (103 samples, 0.02%)')" onmouseout="c()">
<title>native_apic_mem_write (103 samples, 0.02%)</title><rect x="1045.3" y="385" width="0.3" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (348 samples, 0.08%)')" onmouseout="c()">
<title>__rb_rotate_right (348 samples, 0.08%)</title><rect x="120.7" y="273" width="0.9" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_enable (971 samples, 0.22%)')" onmouseout="c()">
<title>x86_pmu_enable (971 samples, 0.22%)</title><rect x="210.7" y="193" width="2.5" height="15.0" fill="rgb(232,155,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (166 samples, 0.04%)')" onmouseout="c()">
<title>idle_cpu (166 samples, 0.04%)</title><rect x="406.2" y="305" width="0.5" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_process_tick (2,938 samples, 0.66%)')" onmouseout="c()">
<title>account_process_tick (2,938 samples, 0.66%)</title><rect x="156.2" y="257" width="7.9" height="15.0" fill="rgb(250,142,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_da_write_begin (102 samples, 0.02%)')" onmouseout="c()">
<title>ext4_da_write_begin (102 samples, 0.02%)</title><rect x="974.1" y="273" width="0.3" height="15.0" fill="rgb(210,48,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_adjust_freq_unthr_context (6,123 samples, 1.38%)')" onmouseout="c()">
<title>perf_adjust_freq_unthr_context (6,123 samples, 1.38%)</title><rect x="198.9" y="225" width="16.2" height="15.0" fill="rgb(241,147,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (90 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (90 samples, 0.02%)</title><rect x="755.8" y="273" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_idle (125 samples, 0.03%)')" onmouseout="c()">
<title>intel_idle (125 samples, 0.03%)</title><rect x="1111.5" y="433" width="0.3" height="15.0" fill="rgb(216,81,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (107 samples, 0.02%)')" onmouseout="c()">
<title>schedule (107 samples, 0.02%)</title><rect x="708.9" y="337" width="0.3" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sync_readv_writev (38 samples, 0.01%)')" onmouseout="c()">
<title>do_sync_readv_writev (38 samples, 0.01%)</title><rect x="982.9" y="385" width="0.1" height="15.0" fill="rgb(0,236,188)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_rq_lock (389 samples, 0.09%)')" onmouseout="c()">
<title>task_rq_lock (389 samples, 0.09%)</title><rect x="1026.4" y="257" width="1.1" height="15.0" fill="rgb(231,149,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (220 samples, 0.05%)')" onmouseout="c()">
<title>enqueue_hrtimer (220 samples, 0.05%)</title><rect x="1144.3" y="385" width="0.6" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_check_callbacks (392 samples, 0.09%)')" onmouseout="c()">
<title>rcu_check_callbacks (392 samples, 0.09%)</title><rect x="147.1" y="273" width="1.0" height="15.0" fill="rgb(244,188,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (416 samples, 0.09%)')" onmouseout="c()">
<title>_spin_lock (416 samples, 0.09%)</title><rect x="46.6" y="321" width="1.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_fair (59 samples, 0.01%)')" onmouseout="c()">
<title>put_prev_task_fair (59 samples, 0.01%)</title><rect x="615.9" y="321" width="0.2" height="15.0" fill="rgb(238,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start_range_ns (243 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_start_range_ns (243 samples, 0.05%)</title><rect x="1187.4" y="369" width="0.7" height="15.0" fill="rgb(207,72,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn (49 samples, 0.01%)')" onmouseout="c()">
<title>unalias_gfn (49 samples, 0.01%)</title><rect x="692.6" y="273" width="0.1" height="15.0" fill="rgb(221,189,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (183 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (183 samples, 0.04%)</title><rect x="787.5" y="225" width="0.5" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (126 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock_irq (126 samples, 0.03%)</title><rect x="366.2" y="257" width="0.3" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (42 samples, 0.01%)')" onmouseout="c()">
<title>call_softirq (42 samples, 0.01%)</title><rect x="1176.8" y="305" width="0.1" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cr8_intercept (1,948 samples, 0.44%)')" onmouseout="c()">
<title>update_cr8_intercept (1,948 samples, 0.44%)</title><rect x="920.1" y="353" width="5.2" height="15.0" fill="rgb(242,70,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('deactivate_task (98 samples, 0.02%)')" onmouseout="c()">
<title>deactivate_task (98 samples, 0.02%)</title><rect x="602.2" y="321" width="0.3" height="15.0" fill="rgb(245,205,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtick_start_fair (192 samples, 0.04%)')" onmouseout="c()">
<title>hrtick_start_fair (192 samples, 0.04%)</title><rect x="1119.4" y="401" width="0.5" height="15.0" fill="rgb(209,42,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (47 samples, 0.01%)')" onmouseout="c()">
<title>tick_sched_timer (47 samples, 0.01%)</title><rect x="1030.9" y="353" width="0.1" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (807 samples, 0.18%)')" onmouseout="c()">
<title>vmcs_writel (807 samples, 0.18%)</title><rect x="731.7" y="337" width="2.2" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_pte_fault (1,846 samples, 0.42%)')" onmouseout="c()">
<title>handle_pte_fault (1,846 samples, 0.42%)</title><rect x="758.2" y="177" width="4.9" height="15.0" fill="rgb(0,194,93)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (378 samples, 0.09%)')" onmouseout="c()">
<title>tick_program_event (378 samples, 0.09%)</title><rect x="1145.4" y="385" width="1.0" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tr_desc (44 samples, 0.01%)')" onmouseout="c()">
<title>native_load_tr_desc (44 samples, 0.01%)</title><rect x="688.1" y="273" width="0.1" height="15.0" fill="rgb(229,52,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_next_entity (121 samples, 0.03%)')" onmouseout="c()">
<title>set_next_entity (121 samples, 0.03%)</title><rect x="1124.6" y="417" width="0.3" height="15.0" fill="rgb(232,112,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpumask_next_and (1,184 samples, 0.27%)')" onmouseout="c()">
<title>cpumask_next_and (1,184 samples, 0.27%)</title><rect x="664.6" y="289" width="3.1" height="15.0" fill="rgb(231,93,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('br_handle_frame (42 samples, 0.01%)')" onmouseout="c()">
<title>br_handle_frame (42 samples, 0.01%)</title><rect x="914.8" y="145" width="0.1" height="15.0" fill="rgb(244,10,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vsnprintf (93 samples, 0.02%)')" onmouseout="c()">
<title>vsnprintf (93 samples, 0.02%)</title><rect x="973.0" y="305" width="0.2" height="15.0" fill="rgb(215,213,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_enter_nohz (157 samples, 0.04%)')" onmouseout="c()">
<title>rcu_enter_nohz (157 samples, 0.04%)</title><rect x="1168.7" y="417" width="0.5" height="15.0" fill="rgb(229,176,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (76 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_start (76 samples, 0.02%)</title><rect x="1111.1" y="433" width="0.2" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (128 samples, 0.03%)')" onmouseout="c()">
<title>native_read_tsc (128 samples, 0.03%)</title><rect x="680.2" y="225" width="0.3" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (220 samples, 0.05%)')" onmouseout="c()">
<title>tick_program_event (220 samples, 0.05%)</title><rect x="1162.6" y="369" width="0.6" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_interrupt_shadow (357 samples, 0.08%)')" onmouseout="c()">
<title>vmx_set_interrupt_shadow (357 samples, 0.08%)</title><rect x="771.6" y="289" width="0.9" height="15.0" fill="rgb(218,142,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_fair (423 samples, 0.10%)')" onmouseout="c()">
<title>put_prev_task_fair (423 samples, 0.10%)</title><rect x="619.7" y="305" width="1.1" height="15.0" fill="rgb(238,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (58 samples, 0.01%)')" onmouseout="c()">
<title>__local_bh_enable (58 samples, 0.01%)</title><rect x="1037.0" y="353" width="0.1" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (179 samples, 0.04%)')" onmouseout="c()">
<title>read_tsc (179 samples, 0.04%)</title><rect x="820.7" y="225" width="0.5" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ioeventfd_write (77 samples, 0.02%)')" onmouseout="c()">
<title>ioeventfd_write (77 samples, 0.02%)</title><rect x="766.7" y="273" width="0.2" height="15.0" fill="rgb(0,214,155)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mmu_topup_memory_caches (50 samples, 0.01%)')" onmouseout="c()">
<title>mmu_topup_memory_caches (50 samples, 0.01%)</title><rect x="763.9" y="273" width="0.1" height="15.0" fill="rgb(0,222,115)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_process_times (51,499 samples, 11.60%)')" onmouseout="c()">
<title>update_process_times (51,499 samples, 11.60%)</title><rect x="153.9" y="273" width="136.9" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
<text text-anchor="" x="156.948917193148" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >update_process_ti..</text>
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_page (879 samples, 0.20%)')" onmouseout="c()">
<title>kvm_write_guest_page (879 samples, 0.20%)</title><rect x="691.1" y="321" width="2.4" height="15.0" fill="rgb(245,123,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (39 samples, 0.01%)')" onmouseout="c()">
<title>finish_task_switch (39 samples, 0.01%)</title><rect x="1110.5" y="433" width="0.1" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (596 samples, 0.13%)')" onmouseout="c()">
<title>update_cfs_load (596 samples, 0.13%)</title><rect x="631.6" y="241" width="1.6" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn (50 samples, 0.01%)')" onmouseout="c()">
<title>unalias_gfn (50 samples, 0.01%)</title><rect x="692.8" y="289" width="0.1" height="15.0" fill="rgb(221,189,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (39 samples, 0.01%)')" onmouseout="c()">
<title>apic_timer_interrupt (39 samples, 0.01%)</title><rect x="602.1" y="321" width="0.1" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_read (70 samples, 0.02%)')" onmouseout="c()">
<title>vfs_read (70 samples, 0.02%)</title><rect x="11.0" y="369" width="0.2" height="15.0" fill="rgb(215,113,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (248 samples, 0.06%)')" onmouseout="c()">
<title>hrtimer_forward (248 samples, 0.06%)</title><rect x="141.3" y="273" width="0.6" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (162 samples, 0.04%)')" onmouseout="c()">
<title>[unknown] (162 samples, 0.04%)</title><rect x="1171.9" y="433" width="0.5" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (234 samples, 0.05%)')" onmouseout="c()">
<title>_spin_lock_irqsave (234 samples, 0.05%)</title><rect x="614.9" y="305" width="0.7" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (85 samples, 0.02%)')" onmouseout="c()">
<title>find_busiest_group (85 samples, 0.02%)</title><rect x="1184.6" y="225" width="0.2" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (274 samples, 0.06%)')" onmouseout="c()">
<title>tick_dev_program_event (274 samples, 0.06%)</title><rect x="1145.6" y="369" width="0.8" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_idle_wakeup_event (94 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock_idle_wakeup_event (94 samples, 0.02%)</title><rect x="1041.5" y="337" width="0.3" height="15.0" fill="rgb(230,105,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (344 samples, 0.08%)')" onmouseout="c()">
<title>sched_clock_tick (344 samples, 0.08%)</title><rect x="181.9" y="257" width="0.9" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (113 samples, 0.03%)')" onmouseout="c()">
<title>native_read_tsc (113 samples, 0.03%)</title><rect x="297.7" y="289" width="0.3" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_page (92 samples, 0.02%)')" onmouseout="c()">
<title>kvm_read_guest_page (92 samples, 0.02%)</title><rect x="598.7" y="337" width="0.2" height="15.0" fill="rgb(221,162,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (50 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (50 samples, 0.01%)</title><rect x="685.1" y="289" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (109 samples, 0.02%)')" onmouseout="c()">
<title>idle_cpu (109 samples, 0.02%)</title><rect x="814.2" y="193" width="0.3" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_idle (1,826 samples, 0.41%)')" onmouseout="c()">
<title>tick_check_idle (1,826 samples, 0.41%)</title><rect x="1038.1" y="369" width="4.8" height="15.0" fill="rgb(235,88,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_sched_out (222 samples, 0.05%)')" onmouseout="c()">
<title>perf_event_task_sched_out (222 samples, 0.05%)</title><rect x="618.5" y="305" width="0.6" height="15.0" fill="rgb(215,151,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_set_eoi (1,627 samples, 0.37%)')" onmouseout="c()">
<title>apic_set_eoi (1,627 samples, 0.37%)</title><rect x="563.8" y="321" width="4.3" height="15.0" fill="rgb(250,72,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_commit_txn (95 samples, 0.02%)')" onmouseout="c()">
<title>x86_pmu_commit_txn (95 samples, 0.02%)</title><rect x="1046.5" y="321" width="0.2" height="15.0" fill="rgb(234,33,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (274 samples, 0.06%)')" onmouseout="c()">
<title>clockevents_program_event (274 samples, 0.06%)</title><rect x="1035.1" y="337" width="0.7" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (99 samples, 0.02%)')" onmouseout="c()">
<title>copy_user_generic_string (99 samples, 0.02%)</title><rect x="974.6" y="257" width="0.2" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (761 samples, 0.17%)')" onmouseout="c()">
<title>irq_exit (761 samples, 0.17%)</title><rect x="1106.7" y="369" width="2.0" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mem_cgroup_newpage_charge (196 samples, 0.04%)')" onmouseout="c()">
<title>mem_cgroup_newpage_charge (196 samples, 0.04%)</title><rect x="762.2" y="161" width="0.5" height="15.0" fill="rgb(0,227,128)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('memcpy (213 samples, 0.05%)')" onmouseout="c()">
<title>memcpy (213 samples, 0.05%)</title><rect x="610.6" y="321" width="0.5" height="15.0" fill="rgb(218,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_pid_task (52 samples, 0.01%)')" onmouseout="c()">
<title>get_pid_task (52 samples, 0.01%)</title><rect x="767.1" y="289" width="0.1" height="15.0" fill="rgb(227,156,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_rq_lock (71 samples, 0.02%)')" onmouseout="c()">
<title>task_rq_lock (71 samples, 0.02%)</title><rect x="798.4" y="97" width="0.2" height="15.0" fill="rgb(231,149,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__switch_to (102 samples, 0.02%)')" onmouseout="c()">
<title>__switch_to (102 samples, 0.02%)</title><rect x="1172.5" y="449" width="0.3" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_queue (44 samples, 0.01%)')" onmouseout="c()">
<title>find_busiest_queue (44 samples, 0.01%)</title><rect x="602.6" y="321" width="0.1" height="15.0" fill="rgb(251,226,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (89 samples, 0.02%)')" onmouseout="c()">
<title>apic_update_ppr (89 samples, 0.02%)</title><rect x="606.3" y="289" width="0.2" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_cache_reg (2,076 samples, 0.47%)')" onmouseout="c()">
<title>vmx_cache_reg (2,076 samples, 0.47%)</title><rect x="733.9" y="337" width="5.5" height="15.0" fill="rgb(209,28,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (241 samples, 0.05%)')" onmouseout="c()">
<title>irq_exit (241 samples, 0.05%)</title><rect x="1184.5" y="321" width="0.6" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('deactivate_task (8,910 samples, 2.01%)')" onmouseout="c()">
<title>deactivate_task (8,910 samples, 2.01%)</title><rect x="626.8" y="305" width="23.7" height="15.0" fill="rgb(245,205,36)" rx="2" ry="2" />
<text text-anchor="" x="629.833947092807" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s('ktime_get (93 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (93 samples, 0.02%)</title><rect x="1146.1" y="353" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (64 samples, 0.01%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (64 samples, 0.01%)</title><rect x="1146.4" y="401" width="0.1" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (1,450 samples, 0.33%)')" onmouseout="c()">
<title>tick_dev_program_event (1,450 samples, 0.33%)</title><rect x="303.3" y="305" width="3.9" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (88 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_forward (88 samples, 0.02%)</title><rect x="1110.9" y="433" width="0.2" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_cached (898 samples, 0.20%)')" onmouseout="c()">
<title>kvm_write_guest_cached (898 samples, 0.20%)</title><rect x="588.7" y="321" width="2.3" height="15.0" fill="rgb(227,3,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('force_quiescent_state (540 samples, 0.12%)')" onmouseout="c()">
<title>force_quiescent_state (540 samples, 0.12%)</title><rect x="389.4" y="241" width="1.5" height="15.0" fill="rgb(244,88,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (38 samples, 0.01%)')" onmouseout="c()">
<title>lapic_next_event (38 samples, 0.01%)</title><rect x="1036.2" y="337" width="0.1" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_nohz_load_balancer (204 samples, 0.05%)')" onmouseout="c()">
<title>select_nohz_load_balancer (204 samples, 0.05%)</title><rect x="1170.1" y="417" width="0.5" height="15.0" fill="rgb(224,12,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__mem_cgroup_commit_charge (66 samples, 0.01%)')" onmouseout="c()">
<title>__mem_cgroup_commit_charge (66 samples, 0.01%)</title><rect x="762.3" y="129" width="0.1" height="15.0" fill="rgb(0,238,90)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (3,501 samples, 0.79%)')" onmouseout="c()">
<title>update_cfs_shares (3,501 samples, 0.79%)</title><rect x="633.2" y="241" width="9.3" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (58 samples, 0.01%)')" onmouseout="c()">
<title>sched_clock_tick (58 samples, 0.01%)</title><rect x="1041.8" y="337" width="0.1" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (165 samples, 0.04%)')" onmouseout="c()">
<title>calc_delta_mine (165 samples, 0.04%)</title><rect x="642.1" y="209" width="0.4" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpuidle_idle_call (46,869 samples, 10.56%)')" onmouseout="c()">
<title>cpuidle_idle_call (46,869 samples, 10.56%)</title><rect x="985.3" y="433" width="124.5" height="15.0" fill="rgb(214,193,0)" rx="2" ry="2" />
<text text-anchor="" x="988.276371949503" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpuidle_idle_call</text>
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (59 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (59 samples, 0.01%)</title><rect x="973.7" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_apic_timer_irqs (4,090 samples, 0.92%)')" onmouseout="c()">
<title>kvm_inject_apic_timer_irqs (4,090 samples, 0.92%)</title><rect x="542.8" y="321" width="10.9" height="15.0" fill="rgb(222,210,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (192 samples, 0.04%)')" onmouseout="c()">
<title>_spin_lock (192 samples, 0.04%)</title><rect x="155.7" y="257" width="0.5" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cr8_intercept (3,935 samples, 0.89%)')" onmouseout="c()">
<title>update_cr8_intercept (3,935 samples, 0.89%)</title><rect x="720.4" y="337" width="10.4" height="15.0" fill="rgb(242,70,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_task_rq_fair (892 samples, 0.20%)')" onmouseout="c()">
<title>select_task_rq_fair (892 samples, 0.20%)</title><rect x="1024.1" y="257" width="2.3" height="15.0" fill="rgb(210,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (47 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (47 samples, 0.01%)</title><rect x="1032.2" y="369" width="0.1" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_runnable (1,836 samples, 0.41%)')" onmouseout="c()">
<title>kvm_arch_vcpu_runnable (1,836 samples, 0.41%)</title><rect x="604.7" y="321" width="4.8" height="15.0" fill="rgb(252,200,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (706 samples, 0.16%)')" onmouseout="c()">
<title>kvm_timer_fn (706 samples, 0.16%)</title><rect x="1174.0" y="305" width="1.8" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (42 samples, 0.01%)')" onmouseout="c()">
<title>__do_softirq (42 samples, 0.01%)</title><rect x="1176.8" y="289" width="0.1" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_lock (115 samples, 0.03%)')" onmouseout="c()">
<title>srcu_read_lock (115 samples, 0.03%)</title><rect x="918.8" y="353" width="0.3" height="15.0" fill="rgb(236,159,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (300 samples, 0.07%)')" onmouseout="c()">
<title>_cond_resched (300 samples, 0.07%)</title><rect x="563.0" y="321" width="0.8" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (18,519 samples, 4.17%)')" onmouseout="c()">
<title>call_softirq (18,519 samples, 4.17%)</title><rect x="357.0" y="289" width="49.2" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
<text text-anchor="" x="360.003479847292" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >call..</text>
</g>
<g class="func_g" onmouseover="s('rb_erase (298 samples, 0.07%)')" onmouseout="c()">
<title>rb_erase (298 samples, 0.07%)</title><rect x="1166.4" y="385" width="0.8" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (156 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (156 samples, 0.04%)</title><rect x="1143.9" y="385" width="0.4" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timespec (50 samples, 0.01%)')" onmouseout="c()">
<title>ns_to_timespec (50 samples, 0.01%)</title><rect x="1104.9" y="401" width="0.1" height="15.0" fill="rgb(227,143,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (255 samples, 0.06%)')" onmouseout="c()">
<title>enqueue_hrtimer (255 samples, 0.06%)</title><rect x="817.4" y="209" width="0.7" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (79 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (79 samples, 0.02%)</title><rect x="1111.8" y="433" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (764 samples, 0.17%)')" onmouseout="c()">
<title>hrtimer_interrupt (764 samples, 0.17%)</title><rect x="32.4" y="337" width="2.0" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_write (353 samples, 0.08%)')" onmouseout="c()">
<title>sys_write (353 samples, 0.08%)</title><rect x="974.0" y="385" width="0.9" height="15.0" fill="rgb(205,146,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpumask_next_and (250 samples, 0.06%)')" onmouseout="c()">
<title>cpumask_next_and (250 samples, 0.06%)</title><rect x="626.2" y="305" width="0.6" height="15.0" fill="rgb(231,93,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_posix_cpu_timers (217 samples, 0.05%)')" onmouseout="c()">
<title>run_posix_cpu_timers (217 samples, 0.05%)</title><rect x="148.5" y="273" width="0.5" height="15.0" fill="rgb(243,97,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (353 samples, 0.08%)')" onmouseout="c()">
<title>system_call_fastpath (353 samples, 0.08%)</title><rect x="974.0" y="401" width="0.9" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_mmu_page_fault (3,675 samples, 0.83%)')" onmouseout="c()">
<title>kvm_mmu_page_fault (3,675 samples, 0.83%)</title><rect x="754.3" y="305" width="9.8" height="15.0" fill="rgb(0,206,170)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_resched (65 samples, 0.01%)')" onmouseout="c()">
<title>kvm_resched (65 samples, 0.01%)</title><rect x="598.9" y="337" width="0.2" height="15.0" fill="rgb(237,225,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('getnstimeofday (474 samples, 0.11%)')" onmouseout="c()">
<title>getnstimeofday (474 samples, 0.11%)</title><rect x="1098.7" y="385" width="1.2" height="15.0" fill="rgb(214,199,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_get_msr (99 samples, 0.02%)')" onmouseout="c()">
<title>vmx_get_msr (99 samples, 0.02%)</title><rect x="685.2" y="289" width="0.3" height="15.0" fill="rgb(217,85,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('profile_tick (911 samples, 0.21%)')" onmouseout="c()">
<title>profile_tick (911 samples, 0.21%)</title><rect x="144.6" y="273" width="2.5" height="15.0" fill="rgb(220,196,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (58 samples, 0.01%)')" onmouseout="c()">
<title>__run_hrtimer (58 samples, 0.01%)</title><rect x="991.8" y="385" width="0.2" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('skip_emulated_instruction (839 samples, 0.19%)')" onmouseout="c()">
<title>skip_emulated_instruction (839 samples, 0.19%)</title><rect x="770.3" y="305" width="2.2" height="15.0" fill="rgb(220,204,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__apic_accept_irq (991 samples, 0.22%)')" onmouseout="c()">
<title>__apic_accept_irq (991 samples, 0.22%)</title><rect x="796.2" y="209" width="2.6" height="15.0" fill="rgb(223,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_pid_status (109 samples, 0.02%)')" onmouseout="c()">
<title>proc_pid_status (109 samples, 0.02%)</title><rect x="972.5" y="369" width="0.3" height="15.0" fill="rgb(216,44,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (68 samples, 0.02%)')" onmouseout="c()">
<title>irq_enter (68 samples, 0.02%)</title><rect x="990.0" y="401" width="0.2" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_readdir (54 samples, 0.01%)')" onmouseout="c()">
<title>vfs_readdir (54 samples, 0.01%)</title><rect x="973.7" y="417" width="0.1" height="15.0" fill="rgb(245,38,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (93 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (93 samples, 0.02%)</title><rect x="757.8" y="177" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (375 samples, 0.08%)')" onmouseout="c()">
<title>__local_bh_enable (375 samples, 0.08%)</title><rect x="401.7" y="273" width="1.0" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (74 samples, 0.02%)')" onmouseout="c()">
<title>rb_insert_color (74 samples, 0.02%)</title><rect x="1167.2" y="385" width="0.2" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_pending (183 samples, 0.04%)')" onmouseout="c()">
<title>hrtimer_run_pending (183 samples, 0.04%)</title><rect x="401.1" y="241" width="0.4" height="15.0" fill="rgb(230,45,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (74 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (74 samples, 0.02%)</title><rect x="1030.7" y="353" width="0.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (85 samples, 0.02%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (85 samples, 0.02%)</title><rect x="603.5" y="305" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (56 samples, 0.01%)')" onmouseout="c()">
<title>lapic_next_event (56 samples, 0.01%)</title><rect x="1160.8" y="321" width="0.2" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (242 samples, 0.05%)')" onmouseout="c()">
<title>native_write_msr_safe (242 samples, 0.05%)</title><rect x="687.4" y="257" width="0.7" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (682 samples, 0.15%)')" onmouseout="c()">
<title>do_softirq (682 samples, 0.15%)</title><rect x="47.7" y="321" width="1.8" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (297 samples, 0.07%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (297 samples, 0.07%)</title><rect x="1165.1" y="385" width="0.8" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (480 samples, 0.11%)')" onmouseout="c()">
<title>update_rq_clock (480 samples, 0.11%)</title><rect x="679.2" y="289" width="1.3" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_to_vapic (790 samples, 0.18%)')" onmouseout="c()">
<title>kvm_lapic_sync_to_vapic (790 samples, 0.18%)</title><rect x="903.4" y="353" width="2.1" height="15.0" fill="rgb(227,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll (84 samples, 0.02%)')" onmouseout="c()">
<title>bnx2_poll (84 samples, 0.02%)</title><rect x="914.7" y="241" width="0.2" height="15.0" fill="rgb(238,178,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (131 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (131 samples, 0.03%)</title><rect x="1188.8" y="321" width="0.3" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (97 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (97 samples, 0.02%)</title><rect x="198.6" y="225" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (111 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (111 samples, 0.03%)</title><rect x="601.8" y="321" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('worker_thread (523 samples, 0.12%)')" onmouseout="c()">
<title>worker_thread (523 samples, 0.12%)</title><rect x="979.1" y="433" width="1.4" height="15.0" fill="rgb(216,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (412 samples, 0.09%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (412 samples, 0.09%)</title><rect x="917.7" y="353" width="1.1" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('prepare_to_wait (92 samples, 0.02%)')" onmouseout="c()">
<title>prepare_to_wait (92 samples, 0.02%)</title><rect x="707.9" y="337" width="0.3" height="15.0" fill="rgb(227,79,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scsi_io_completion (51 samples, 0.01%)')" onmouseout="c()">
<title>scsi_io_completion (51 samples, 0.01%)</title><rect x="914.5" y="209" width="0.2" height="15.0" fill="rgb(0,200,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (135 samples, 0.03%)')" onmouseout="c()">
<title>native_read_msr_safe (135 samples, 0.03%)</title><rect x="698.7" y="337" width="0.4" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_load_guest_fpu (1,239 samples, 0.28%)')" onmouseout="c()">
<title>kvm_load_guest_fpu (1,239 samples, 0.28%)</title><rect x="905.5" y="353" width="3.3" height="15.0" fill="rgb(222,228,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_send_IPI_mask_sequence_phys (70 samples, 0.02%)')" onmouseout="c()">
<title>default_send_IPI_mask_sequence_phys (70 samples, 0.02%)</title><rect x="191.8" y="209" width="0.2" height="15.0" fill="rgb(237,15,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ntp_tick_length (40 samples, 0.01%)')" onmouseout="c()">
<title>ntp_tick_length (40 samples, 0.01%)</title><rect x="153.8" y="257" width="0.1" height="15.0" fill="rgb(212,167,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_real (693 samples, 0.16%)')" onmouseout="c()">
<title>ktime_get_real (693 samples, 0.16%)</title><rect x="1098.5" y="401" width="1.8" height="15.0" fill="rgb(235,131,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cpu_load (1,351 samples, 0.30%)')" onmouseout="c()">
<title>update_cpu_load (1,351 samples, 0.30%)</title><rect x="277.0" y="241" width="3.6" height="15.0" fill="rgb(210,180,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_work_run (509 samples, 0.11%)')" onmouseout="c()">
<title>irq_work_run (509 samples, 0.11%)</title><rect x="165.7" y="257" width="1.4" height="15.0" fill="rgb(252,101,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (410 samples, 0.09%)')" onmouseout="c()">
<title>[unknown] (410 samples, 0.09%)</title><rect x="973.9" y="433" width="1.1" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (3,330 samples, 0.75%)')" onmouseout="c()">
<title>__remove_hrtimer (3,330 samples, 0.75%)</title><rect x="106.4" y="289" width="8.8" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (10,684 samples, 2.41%)')" onmouseout="c()">
<title>autoremove_wake_function (10,684 samples, 2.41%)</title><rect x="1000.0" y="305" width="28.4" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
<text text-anchor="" x="1002.97903082311" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >au..</text>
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (440 samples, 0.10%)')" onmouseout="c()">
<title>_spin_lock_irqsave (440 samples, 0.10%)</title><rect x="814.8" y="177" width="1.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (58 samples, 0.01%)')" onmouseout="c()">
<title>apic_timer_interrupt (58 samples, 0.01%)</title><rect x="626.0" y="305" width="0.2" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (345 samples, 0.08%)')" onmouseout="c()">
<title>rb_insert_color (345 samples, 0.08%)</title><rect x="1164.2" y="369" width="0.9" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_enable (1,003 samples, 0.23%)')" onmouseout="c()">
<title>perf_pmu_enable (1,003 samples, 0.23%)</title><rect x="210.6" y="209" width="2.6" height="15.0" fill="rgb(238,51,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (10,920 samples, 2.46%)')" onmouseout="c()">
<title>__wake_up_common (10,920 samples, 2.46%)</title><rect x="999.6" y="321" width="29.0" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
<text text-anchor="" x="1002.59631519083" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('thread_return (74 samples, 0.02%)')" onmouseout="c()">
<title>thread_return (74 samples, 0.02%)</title><rect x="980.2" y="417" width="0.2" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (460 samples, 0.10%)')" onmouseout="c()">
<title>native_read_msr_safe (460 samples, 0.10%)</title><rect x="837.5" y="321" width="1.2" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_exit_nohz (65 samples, 0.01%)')" onmouseout="c()">
<title>rcu_exit_nohz (65 samples, 0.01%)</title><rect x="1113.2" y="433" width="0.2" height="15.0" fill="rgb(242,77,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (363 samples, 0.08%)')" onmouseout="c()">
<title>__rb_rotate_left (363 samples, 0.08%)</title><rect x="812.6" y="161" width="0.9" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('____pagevec_lru_add (78 samples, 0.02%)')" onmouseout="c()">
<title>____pagevec_lru_add (78 samples, 0.02%)</title><rect x="762.8" y="113" width="0.2" height="15.0" fill="rgb(0,205,200)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_of (82 samples, 0.02%)')" onmouseout="c()">
<title>task_of (82 samples, 0.02%)</title><rect x="1019.7" y="209" width="0.2" height="15.0" fill="rgb(226,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('retint_restore_args (288 samples, 0.06%)')" onmouseout="c()">
<title>retint_restore_args (288 samples, 0.06%)</title><rect x="915.1" y="353" width="0.8" height="15.0" fill="rgb(235,139,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (3,135 samples, 0.71%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (3,135 samples, 0.71%)</title><rect x="523.3" y="321" width="8.3" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('PyEval_EvalFrameEx (61 samples, 0.01%)')" onmouseout="c()">
<title>PyEval_EvalFrameEx (61 samples, 0.01%)</title><rect x="10.0" y="465" width="0.2" height="15.0" fill="rgb(230,68,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (93 samples, 0.02%)')" onmouseout="c()">
<title>system_call_fastpath (93 samples, 0.02%)</title><rect x="11.0" y="401" width="0.2" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task (8,656 samples, 1.95%)')" onmouseout="c()">
<title>dequeue_task (8,656 samples, 1.95%)</title><rect x="626.9" y="289" width="23.0" height="15.0" fill="rgb(236,71,32)" rx="2" ry="2" />
<text text-anchor="" x="629.887102041736" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s('do_task_stat (153 samples, 0.03%)')" onmouseout="c()">
<title>do_task_stat (153 samples, 0.03%)</title><rect x="972.8" y="353" width="0.4" height="15.0" fill="rgb(221,62,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (1,118 samples, 0.25%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (1,118 samples, 0.25%)</title><rect x="605.6" y="305" width="3.0" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_lock (123 samples, 0.03%)')" onmouseout="c()">
<title>mutex_lock (123 samples, 0.03%)</title><rect x="611.3" y="321" width="0.4" height="15.0" fill="rgb(238,5,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_stop (209 samples, 0.05%)')" onmouseout="c()">
<title>pick_next_task_stop (209 samples, 0.05%)</title><rect x="671.8" y="305" width="0.6" height="15.0" fill="rgb(220,169,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (103 samples, 0.02%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (103 samples, 0.02%)</title><rect x="388.8" y="241" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_user_pages_fast (2,390 samples, 0.54%)')" onmouseout="c()">
<title>get_user_pages_fast (2,390 samples, 0.54%)</title><rect x="757.2" y="241" width="6.4" height="15.0" fill="rgb(0,196,67)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (61 samples, 0.01%)')" onmouseout="c()">
<title>__do_softirq (61 samples, 0.01%)</title><rect x="1185.2" y="289" width="0.1" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_cr0 (279 samples, 0.06%)')" onmouseout="c()">
<title>native_read_cr0 (279 samples, 0.06%)</title><rect x="882.5" y="321" width="0.8" height="15.0" fill="rgb(248,172,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (326 samples, 0.07%)')" onmouseout="c()">
<title>sched_clock_cpu (326 samples, 0.07%)</title><rect x="649.0" y="257" width="0.9" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (2,175 samples, 0.49%)')" onmouseout="c()">
<title>hrtimer_cancel (2,175 samples, 0.49%)</title><rect x="787.0" y="241" width="5.7" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_needs_cpu (120 samples, 0.03%)')" onmouseout="c()">
<title>rcu_needs_cpu (120 samples, 0.03%)</title><rect x="1169.2" y="417" width="0.3" height="15.0" fill="rgb(243,9,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (143 samples, 0.03%)')" onmouseout="c()">
<title>__list_add (143 samples, 0.03%)</title><rect x="600.9" y="321" width="0.4" height="15.0" fill="rgb(223,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (765 samples, 0.17%)')" onmouseout="c()">
<title>try_to_wake_up (765 samples, 0.17%)</title><rect x="796.5" y="113" width="2.1" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (656 samples, 0.15%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (656 samples, 0.15%)</title><rect x="586.9" y="321" width="1.8" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_gp_end (2,105 samples, 0.47%)')" onmouseout="c()">
<title>rcu_process_gp_end (2,105 samples, 0.47%)</title><rect x="382.5" y="225" width="5.6" height="15.0" fill="rgb(245,96,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_task_sched_out (162 samples, 0.04%)')" onmouseout="c()">
<title>__perf_event_task_sched_out (162 samples, 0.04%)</title><rect x="1117.4" y="401" width="0.5" height="15.0" fill="rgb(233,222,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_aio_write (345 samples, 0.08%)')" onmouseout="c()">
<title>generic_file_aio_write (345 samples, 0.08%)</title><rect x="974.0" y="321" width="0.9" height="15.0" fill="rgb(233,76,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (60 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (60 samples, 0.01%)</title><rect x="1153.8" y="385" width="0.2" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__dequeue_entity (553 samples, 0.12%)')" onmouseout="c()">
<title>__dequeue_entity (553 samples, 0.12%)</title><rect x="1121.1" y="385" width="1.5" height="15.0" fill="rgb(254,73,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ret_from_intr (90 samples, 0.02%)')" onmouseout="c()">
<title>ret_from_intr (90 samples, 0.02%)</title><rect x="1185.1" y="369" width="0.2" height="15.0" fill="rgb(237,83,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_real (43 samples, 0.01%)')" onmouseout="c()">
<title>ktime_get_real (43 samples, 0.01%)</title><rect x="1183.8" y="353" width="0.2" height="15.0" fill="rgb(235,131,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_select (106 samples, 0.02%)')" onmouseout="c()">
<title>menu_select (106 samples, 0.02%)</title><rect x="1184.1" y="369" width="0.3" height="15.0" fill="rgb(249,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (512 samples, 0.12%)')" onmouseout="c()">
<title>activate_task (512 samples, 0.12%)</title><rect x="796.7" y="97" width="1.4" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (2,966 samples, 0.67%)')" onmouseout="c()">
<title>update_shares (2,966 samples, 0.67%)</title><rect x="672.6" y="305" width="7.9" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (198 samples, 0.04%)')" onmouseout="c()">
<title>do_softirq (198 samples, 0.04%)</title><rect x="1127.2" y="369" width="0.5" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (3,369 samples, 0.76%)')" onmouseout="c()">
<title>run_timer_softirq (3,369 samples, 0.76%)</title><rect x="392.7" y="257" width="9.0" height="15.0" fill="rgb(219,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (281 samples, 0.06%)')" onmouseout="c()">
<title>tick_dev_program_event (281 samples, 0.06%)</title><rect x="1133.4" y="337" width="0.7" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (117 samples, 0.03%)')" onmouseout="c()">
<title>copy_user_generic_string (117 samples, 0.03%)</title><rect x="690.7" y="321" width="0.4" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (39 samples, 0.01%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (39 samples, 0.01%)</title><rect x="236.8" y="193" width="0.1" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (128 samples, 0.03%)')" onmouseout="c()">
<title>lapic_next_event (128 samples, 0.03%)</title><rect x="336.7" y="257" width="0.4" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_load (193 samples, 0.04%)')" onmouseout="c()">
<title>vcpu_load (193 samples, 0.04%)</title><rect x="730.9" y="337" width="0.5" height="15.0" fill="rgb(220,63,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (44 samples, 0.01%)')" onmouseout="c()">
<title>schedule (44 samples, 0.01%)</title><rect x="599.0" y="289" width="0.1" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (91 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_shares (91 samples, 0.02%)</title><rect x="647.9" y="257" width="0.3" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (64 samples, 0.01%)')" onmouseout="c()">
<title>call_softirq (64 samples, 0.01%)</title><rect x="1185.2" y="305" width="0.1" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (54 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (54 samples, 0.01%)</title><rect x="1169.5" y="417" width="0.1" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_write_end (68 samples, 0.02%)')" onmouseout="c()">
<title>generic_write_end (68 samples, 0.02%)</title><rect x="974.4" y="257" width="0.2" height="15.0" fill="rgb(220,178,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_load (1,423 samples, 0.32%)')" onmouseout="c()">
<title>vcpu_load (1,423 samples, 0.32%)</title><rect x="680.5" y="321" width="3.8" height="15.0" fill="rgb(220,63,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (335 samples, 0.08%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (335 samples, 0.08%)</title><rect x="567.2" y="305" width="0.9" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('yield_to (156 samples, 0.04%)')" onmouseout="c()">
<title>yield_to (156 samples, 0.04%)</title><rect x="767.2" y="289" width="0.4" height="15.0" fill="rgb(209,32,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (93 samples, 0.02%)')" onmouseout="c()">
<title>lapic_next_event (93 samples, 0.02%)</title><rect x="1159.4" y="305" width="0.2" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_buffered_write (313 samples, 0.07%)')" onmouseout="c()">
<title>generic_file_buffered_write (313 samples, 0.07%)</title><rect x="974.1" y="289" width="0.8" height="15.0" fill="rgb(206,181,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (114 samples, 0.03%)')" onmouseout="c()">
<title>__remove_hrtimer (114 samples, 0.03%)</title><rect x="100.9" y="305" width="0.3" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (198 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (198 samples, 0.04%)</title><rect x="377.6" y="225" width="0.5" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (62 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (62 samples, 0.01%)</title><rect x="1148.1" y="417" width="0.2" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_msr_common (17,145 samples, 3.86%)')" onmouseout="c()">
<title>kvm_set_msr_common (17,145 samples, 3.86%)</title><rect x="777.0" y="289" width="45.6" height="15.0" fill="rgb(225,95,21)" rx="2" ry="2" />
<text text-anchor="" x="780.023255290156" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_..</text>
</g>
<g class="func_g" onmouseover="s('poll_idle (127 samples, 0.03%)')" onmouseout="c()">
<title>poll_idle (127 samples, 0.03%)</title><rect x="1106.3" y="417" width="0.3" height="15.0" fill="rgb(232,76,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (123 samples, 0.03%)')" onmouseout="c()">
<title>sched_clock (123 samples, 0.03%)</title><rect x="1021.8" y="193" width="0.3" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_apic_timer_irqs (403 samples, 0.09%)')" onmouseout="c()">
<title>kvm_inject_apic_timer_irqs (403 samples, 0.09%)</title><rect x="540.6" y="337" width="1.0" height="15.0" fill="rgb(222,210,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_task_rq_fair (69 samples, 0.02%)')" onmouseout="c()">
<title>select_task_rq_fair (69 samples, 0.02%)</title><rect x="798.2" y="97" width="0.2" height="15.0" fill="rgb(210,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sys_open (95 samples, 0.02%)')" onmouseout="c()">
<title>do_sys_open (95 samples, 0.02%)</title><rect x="971.9" y="417" width="0.3" height="15.0" fill="rgb(220,64,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (20,818 samples, 4.69%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (20,818 samples, 4.69%)</title><rect x="991.1" y="401" width="55.3" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
<text text-anchor="" x="994.091523362276" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >smp_a..</text>
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (63 samples, 0.01%)')" onmouseout="c()">
<title>account_entity_enqueue (63 samples, 0.01%)</title><rect x="235.8" y="209" width="0.2" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__getdents64 (62 samples, 0.01%)')" onmouseout="c()">
<title>__getdents64 (62 samples, 0.01%)</title><rect x="973.7" y="465" width="0.1" height="15.0" fill="rgb(215,47,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scsi_softirq_done (54 samples, 0.01%)')" onmouseout="c()">
<title>scsi_softirq_done (54 samples, 0.01%)</title><rect x="914.5" y="241" width="0.2" height="15.0" fill="rgb(0,213,106)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (487 samples, 0.11%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (487 samples, 0.11%)</title><rect x="1126.5" y="401" width="1.3" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (207 samples, 0.05%)')" onmouseout="c()">
<title>rebalance_domains (207 samples, 0.05%)</title><rect x="1184.5" y="241" width="0.5" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_unlock (354 samples, 0.08%)')" onmouseout="c()">
<title>srcu_read_unlock (354 samples, 0.08%)</title><rect x="919.1" y="353" width="1.0" height="15.0" fill="rgb(241,146,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (193 samples, 0.04%)')" onmouseout="c()">
<title>__do_softirq (193 samples, 0.04%)</title><rect x="1127.2" y="337" width="0.5" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_madvise (43 samples, 0.01%)')" onmouseout="c()">
<title>sys_madvise (43 samples, 0.01%)</title><rect x="980.6" y="433" width="0.1" height="15.0" fill="rgb(245,84,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest (1,416 samples, 0.32%)')" onmouseout="c()">
<title>kvm_read_guest (1,416 samples, 0.32%)</title><rect x="593.3" y="337" width="3.8" height="15.0" fill="rgb(246,140,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('futex_wait (55 samples, 0.01%)')" onmouseout="c()">
<title>futex_wait (55 samples, 0.01%)</title><rect x="983.7" y="401" width="0.1" height="15.0" fill="rgb(215,192,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_needs_cpu (65 samples, 0.01%)')" onmouseout="c()">
<title>rcu_needs_cpu (65 samples, 0.01%)</title><rect x="1113.4" y="433" width="0.1" height="15.0" fill="rgb(243,9,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (66 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (66 samples, 0.01%)</title><rect x="973.4" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_sp0 (71 samples, 0.02%)')" onmouseout="c()">
<title>native_load_sp0 (71 samples, 0.02%)</title><rect x="13.4" y="433" width="0.2" height="15.0" fill="rgb(243,63,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (197 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (197 samples, 0.04%)</title><rect x="805.1" y="193" width="0.5" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_get_interrupt (84 samples, 0.02%)')" onmouseout="c()">
<title>kvm_cpu_get_interrupt (84 samples, 0.02%)</title><rect x="891.8" y="353" width="0.3" height="15.0" fill="rgb(215,172,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('atomic_notifier_call_chain (102 samples, 0.02%)')" onmouseout="c()">
<title>atomic_notifier_call_chain (102 samples, 0.02%)</title><rect x="992.7" y="369" width="0.3" height="15.0" fill="rgb(249,80,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (99 samples, 0.02%)')" onmouseout="c()">
<title>_cond_resched (99 samples, 0.02%)</title><rect x="572.6" y="305" width="0.2" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (21,997 samples, 4.95%)')" onmouseout="c()">
<title>irq_exit (21,997 samples, 4.95%)</title><rect x="349.7" y="321" width="58.5" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
<text text-anchor="" x="352.747829318558" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >irq_exit</text>
</g>
<g class="func_g" onmouseover="s('rb_next (125 samples, 0.03%)')" onmouseout="c()">
<title>rb_next (125 samples, 0.03%)</title><rect x="1162.3" y="369" width="0.3" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (65 samples, 0.01%)')" onmouseout="c()">
<title>do_softirq (65 samples, 0.01%)</title><rect x="1185.2" y="321" width="0.1" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (72 samples, 0.02%)')" onmouseout="c()">
<title>__wake_up_common (72 samples, 0.02%)</title><rect x="1029.1" y="337" width="0.2" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (566 samples, 0.13%)')" onmouseout="c()">
<title>_spin_lock_irqsave (566 samples, 0.13%)</title><rect x="376.1" y="225" width="1.5" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (398 samples, 0.09%)')" onmouseout="c()">
<title>system_call_fastpath (398 samples, 0.09%)</title><rect x="972.2" y="449" width="1.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_tick_fair (672 samples, 0.15%)')" onmouseout="c()">
<title>task_tick_fair (672 samples, 0.15%)</title><rect x="288.0" y="257" width="1.8" height="15.0" fill="rgb(231,38,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (363 samples, 0.08%)')" onmouseout="c()">
<title>update_curr (363 samples, 0.08%)</title><rect x="236.1" y="209" width="0.9" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (6,903 samples, 1.55%)')" onmouseout="c()">
<title>hrtimer_start (6,903 samples, 1.55%)</title><rect x="800.5" y="225" width="18.3" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (376 samples, 0.08%)')" onmouseout="c()">
<title>enqueue_entity (376 samples, 0.08%)</title><rect x="796.9" y="49" width="1.0" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_enter (539 samples, 0.12%)')" onmouseout="c()">
<title>rcu_irq_enter (539 samples, 0.12%)</title><rect x="348.3" y="305" width="1.4" height="15.0" fill="rgb(233,147,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (315 samples, 0.07%)')" onmouseout="c()">
<title>enqueue_entity (315 samples, 0.07%)</title><rect x="1174.3" y="161" width="0.9" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (2,961 samples, 0.67%)')" onmouseout="c()">
<title>__remove_hrtimer (2,961 samples, 0.67%)</title><rect x="1155.3" y="385" width="7.9" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (168 samples, 0.04%)')" onmouseout="c()">
<title>__do_softirq (168 samples, 0.04%)</title><rect x="1044.0" y="337" width="0.4" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__queue_work (92 samples, 0.02%)')" onmouseout="c()">
<title>__queue_work (92 samples, 0.02%)</title><rect x="400.8" y="225" width="0.3" height="15.0" fill="rgb(243,121,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_memslot (199 samples, 0.04%)')" onmouseout="c()">
<title>gfn_to_memslot (199 samples, 0.04%)</title><rect x="692.2" y="289" width="0.5" height="15.0" fill="rgb(239,135,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_tick (529 samples, 0.12%)')" onmouseout="c()">
<title>printk_tick (529 samples, 0.12%)</title><rect x="167.6" y="257" width="1.4" height="15.0" fill="rgb(211,104,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (425 samples, 0.10%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (425 samples, 0.10%)</title><rect x="1146.5" y="401" width="1.2" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (64 samples, 0.01%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (64 samples, 0.01%)</title><rect x="497.4" y="321" width="0.2" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ret_from_intr (80 samples, 0.02%)')" onmouseout="c()">
<title>ret_from_intr (80 samples, 0.02%)</title><rect x="1108.8" y="417" width="0.3" height="15.0" fill="rgb(237,83,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (67 samples, 0.02%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (67 samples, 0.02%)</title><rect x="605.2" y="305" width="0.2" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (230 samples, 0.05%)')" onmouseout="c()">
<title>_spin_lock_irqsave (230 samples, 0.05%)</title><rect x="675.0" y="289" width="0.6" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (12,882 samples, 2.90%)')" onmouseout="c()">
<title>__run_hrtimer (12,882 samples, 2.90%)</title><rect x="996.8" y="369" width="34.2" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
<text text-anchor="" x="999.792391634853" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('do_filp_open (80 samples, 0.02%)')" onmouseout="c()">
<title>do_filp_open (80 samples, 0.02%)</title><rect x="971.9" y="401" width="0.2" height="15.0" fill="rgb(244,169,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (48 samples, 0.01%)')" onmouseout="c()">
<title>schedule (48 samples, 0.01%)</title><rect x="977.5" y="417" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_idle (94 samples, 0.02%)')" onmouseout="c()">
<title>pick_next_task_idle (94 samples, 0.02%)</title><rect x="612.2" y="321" width="0.3" height="15.0" fill="rgb(240,58,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_idle_ticks (79 samples, 0.02%)')" onmouseout="c()">
<title>account_idle_ticks (79 samples, 0.02%)</title><rect x="1129.4" y="417" width="0.2" height="15.0" fill="rgb(0,198,157)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_interrupt_shadow (105 samples, 0.02%)')" onmouseout="c()">
<title>vmx_set_interrupt_shadow (105 samples, 0.02%)</title><rect x="766.3" y="289" width="0.3" height="15.0" fill="rgb(218,142,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (22,282 samples, 5.02%)')" onmouseout="c()">
<title>apic_timer_interrupt (22,282 samples, 5.02%)</title><rect x="987.2" y="417" width="59.2" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
<text text-anchor="" x="990.200581100713" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >apic_t..</text>
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (223 samples, 0.05%)')" onmouseout="c()">
<title>pick_next_task_fair (223 samples, 0.05%)</title><rect x="619.1" y="305" width="0.6" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (725 samples, 0.16%)')" onmouseout="c()">
<title>clockevents_program_event (725 samples, 0.16%)</title><rect x="313.4" y="289" width="2.0" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (14,656 samples, 3.30%)')" onmouseout="c()">
<title>update_curr (14,656 samples, 3.30%)</title><rect x="237.0" y="225" width="39.0" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
<text text-anchor="" x="240.040733358109" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >upd..</text>
</g>
<g class="func_g" onmouseover="s('native_read_tsc (96 samples, 0.02%)')" onmouseout="c()">
<title>native_read_tsc (96 samples, 0.02%)</title><rect x="820.5" y="193" width="0.2" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_read (394 samples, 0.09%)')" onmouseout="c()">
<title>vfs_read (394 samples, 0.09%)</title><rect x="972.2" y="417" width="1.1" height="15.0" fill="rgb(215,113,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (280 samples, 0.06%)')" onmouseout="c()">
<title>__rb_rotate_right (280 samples, 0.06%)</title><rect x="125.7" y="257" width="0.7" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_spte (123 samples, 0.03%)')" onmouseout="c()">
<title>set_spte (123 samples, 0.03%)</title><rect x="755.4" y="241" width="0.3" height="15.0" fill="rgb(0,226,121)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (52 samples, 0.01%)')" onmouseout="c()">
<title>__rb_rotate_right (52 samples, 0.01%)</title><rect x="1165.0" y="353" width="0.1" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kcs_event (427 samples, 0.10%)')" onmouseout="c()">
<title>kcs_event (427 samples, 0.10%)</title><rect x="977.8" y="401" width="1.1" height="15.0" fill="rgb(227,94,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (530 samples, 0.12%)')" onmouseout="c()">
<title>native_sched_clock (530 samples, 0.12%)</title><rect x="286.6" y="193" width="1.4" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (110,650 samples, 24.92%)')" onmouseout="c()">
<title>hrtimer_interrupt (110,650 samples, 24.92%)</title><rect x="49.9" y="321" width="294.0" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
<text text-anchor="" x="52.8555807065554" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >hrtimer_interrupt</text>
</g>
<g class="func_g" onmouseover="s('native_smp_send_reschedule (132 samples, 0.03%)')" onmouseout="c()">
<title>native_smp_send_reschedule (132 samples, 0.03%)</title><rect x="191.7" y="241" width="0.4" height="15.0" fill="rgb(218,74,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_from_vapic (583 samples, 0.13%)')" onmouseout="c()">
<title>kvm_lapic_sync_from_vapic (583 samples, 0.13%)</title><rect x="901.8" y="353" width="1.6" height="15.0" fill="rgb(239,223,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (847 samples, 0.19%)')" onmouseout="c()">
<title>__wake_up (847 samples, 0.19%)</title><rect x="796.4" y="177" width="2.3" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (787 samples, 0.18%)')" onmouseout="c()">
<title>autoremove_wake_function (787 samples, 0.18%)</title><rect x="796.5" y="145" width="2.1" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (310 samples, 0.07%)')" onmouseout="c()">
<title>__run_hrtimer (310 samples, 0.07%)</title><rect x="45.8" y="321" width="0.8" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_exit_nohz (162 samples, 0.04%)')" onmouseout="c()">
<title>rcu_exit_nohz (162 samples, 0.04%)</title><rect x="1148.5" y="417" width="0.5" height="15.0" fill="rgb(242,77,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (80 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_load (80 samples, 0.02%)</title><rect x="647.7" y="257" width="0.2" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (720 samples, 0.16%)')" onmouseout="c()">
<title>call_softirq (720 samples, 0.16%)</title><rect x="1106.7" y="337" width="2.0" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('iov_iter_copy_from_user_atomic (101 samples, 0.02%)')" onmouseout="c()">
<title>iov_iter_copy_from_user_atomic (101 samples, 0.02%)</title><rect x="974.6" y="273" width="0.2" height="15.0" fill="rgb(227,71,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('raise_softirq (396 samples, 0.09%)')" onmouseout="c()">
<title>raise_softirq (396 samples, 0.09%)</title><rect x="169.0" y="257" width="1.0" height="15.0" fill="rgb(249,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (62 samples, 0.01%)')" onmouseout="c()">
<title>rcu_irq_exit (62 samples, 0.01%)</title><rect x="1045.6" y="385" width="0.2" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kthread (107 samples, 0.02%)')" onmouseout="c()">
<title>kthread (107 samples, 0.02%)</title><rect x="977.1" y="433" width="0.3" height="15.0" fill="rgb(254,163,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (984 samples, 0.22%)')" onmouseout="c()">
<title>update_curr (984 samples, 0.22%)</title><rect x="639.9" y="225" width="2.6" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (666 samples, 0.15%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (666 samples, 0.15%)</title><rect x="1132.4" y="369" width="1.7" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('page_add_new_anon_rmap (157 samples, 0.04%)')" onmouseout="c()">
<title>page_add_new_anon_rmap (157 samples, 0.04%)</title><rect x="762.7" y="161" width="0.4" height="15.0" fill="rgb(0,208,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_user_pages (2,223 samples, 0.50%)')" onmouseout="c()">
<title>get_user_pages (2,223 samples, 0.50%)</title><rect x="757.4" y="225" width="5.9" height="15.0" fill="rgb(0,211,96)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_ept_violation (3,723 samples, 0.84%)')" onmouseout="c()">
<title>handle_ept_violation (3,723 samples, 0.84%)</title><rect x="754.3" y="321" width="9.9" height="15.0" fill="rgb(0,200,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (42 samples, 0.01%)')" onmouseout="c()">
<title>native_apic_mem_write (42 samples, 0.01%)</title><rect x="1189.0" y="257" width="0.1" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (79 samples, 0.02%)')" onmouseout="c()">
<title>read_tsc (79 samples, 0.02%)</title><rect x="144.0" y="257" width="0.2" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (122 samples, 0.03%)')" onmouseout="c()">
<title>lock_hrtimer_base (122 samples, 0.03%)</title><rect x="1137.1" y="385" width="0.3" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (64 samples, 0.01%)')" onmouseout="c()">
<title>apic_update_ppr (64 samples, 0.01%)</title><rect x="506.0" y="321" width="0.1" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (458 samples, 0.10%)')" onmouseout="c()">
<title>enqueue_task_fair (458 samples, 0.10%)</title><rect x="796.7" y="65" width="1.3" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (7,276 samples, 1.64%)')" onmouseout="c()">
<title>enqueue_task (7,276 samples, 1.64%)</title><rect x="1002.8" y="241" width="19.3" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('raise_softirq (488 samples, 0.11%)')" onmouseout="c()">
<title>raise_softirq (488 samples, 0.11%)</title><rect x="174.4" y="241" width="1.3" height="15.0" fill="rgb(249,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (163 samples, 0.04%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (163 samples, 0.04%)</title><rect x="816.5" y="209" width="0.5" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_global_load (40 samples, 0.01%)')" onmouseout="c()">
<title>calc_global_load (40 samples, 0.01%)</title><rect x="152.5" y="241" width="0.1" height="15.0" fill="rgb(228,122,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (82 samples, 0.02%)')" onmouseout="c()">
<title>autoremove_wake_function (82 samples, 0.02%)</title><rect x="400.8" y="161" width="0.3" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_unlock (51 samples, 0.01%)')" onmouseout="c()">
<title>mutex_unlock (51 samples, 0.01%)</title><rect x="688.7" y="305" width="0.2" height="15.0" fill="rgb(235,145,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (91 samples, 0.02%)')" onmouseout="c()">
<title>save_args (91 samples, 0.02%)</title><rect x="983.0" y="465" width="0.2" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_cached (961 samples, 0.22%)')" onmouseout="c()">
<title>kvm_write_guest_cached (961 samples, 0.22%)</title><rect x="693.7" y="337" width="2.6" height="15.0" fill="rgb(227,3,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clear_buddies (52 samples, 0.01%)')" onmouseout="c()">
<title>clear_buddies (52 samples, 0.01%)</title><rect x="1119.3" y="401" width="0.1" height="15.0" fill="rgb(216,188,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_idle (89 samples, 0.02%)')" onmouseout="c()">
<title>tick_nohz_stop_idle (89 samples, 0.02%)</title><rect x="1043.1" y="369" width="0.3" height="15.0" fill="rgb(210,186,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (454 samples, 0.10%)')" onmouseout="c()">
<title>rb_erase (454 samples, 0.10%)</title><rect x="1161.0" y="369" width="1.3" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (70 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (70 samples, 0.02%)</title><rect x="1122.7" y="385" width="0.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (407 samples, 0.09%)')" onmouseout="c()">
<title>native_write_msr_safe (407 samples, 0.09%)</title><rect x="209.5" y="177" width="1.1" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (123 samples, 0.03%)')" onmouseout="c()">
<title>native_sched_clock (123 samples, 0.03%)</title><rect x="649.6" y="225" width="0.3" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__switch_to (81 samples, 0.02%)')" onmouseout="c()">
<title>__switch_to (81 samples, 0.02%)</title><rect x="941.7" y="417" width="0.3" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (76 samples, 0.02%)')" onmouseout="c()">
<title>schedule (76 samples, 0.02%)</title><rect x="1171.4" y="449" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (76 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (76 samples, 0.02%)</title><rect x="601.3" y="321" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('force_quiescent_state (671 samples, 0.15%)')" onmouseout="c()">
<title>force_quiescent_state (671 samples, 0.15%)</title><rect x="380.8" y="225" width="1.7" height="15.0" fill="rgb(244,88,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_kernel (6,443 samples, 1.45%)')" onmouseout="c()">
<title>start_kernel (6,443 samples, 1.45%)</title><rect x="1172.9" y="433" width="17.1" height="15.0" fill="rgb(246,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('nr_iowait_cpu (61 samples, 0.01%)')" onmouseout="c()">
<title>nr_iowait_cpu (61 samples, 0.01%)</title><rect x="1105.6" y="417" width="0.1" height="15.0" fill="rgb(208,117,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (794 samples, 0.18%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (794 samples, 0.18%)</title><rect x="553.7" y="337" width="2.1" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_direct_IO (62 samples, 0.01%)')" onmouseout="c()">
<title>ext4_direct_IO (62 samples, 0.01%)</title><rect x="11.0" y="321" width="0.2" height="15.0" fill="rgb(0,201,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clear_buddies (76 samples, 0.02%)')" onmouseout="c()">
<title>clear_buddies (76 samples, 0.02%)</title><rect x="1117.0" y="417" width="0.2" height="15.0" fill="rgb(216,188,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_file_write (346 samples, 0.08%)')" onmouseout="c()">
<title>ext4_file_write (346 samples, 0.08%)</title><rect x="974.0" y="337" width="0.9" height="15.0" fill="rgb(205,10,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (821 samples, 0.18%)')" onmouseout="c()">
<title>_spin_lock (821 samples, 0.18%)</title><rect x="189.5" y="241" width="2.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (807 samples, 0.18%)')" onmouseout="c()">
<title>__wake_up_common (807 samples, 0.18%)</title><rect x="796.5" y="161" width="2.1" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (97 samples, 0.02%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (97 samples, 0.02%)</title><rect x="625.8" y="305" width="0.2" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (67 samples, 0.02%)')" onmouseout="c()">
<title>irq_exit (67 samples, 0.02%)</title><rect x="1185.2" y="337" width="0.1" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (511 samples, 0.12%)')" onmouseout="c()">
<title>enqueue_task (511 samples, 0.12%)</title><rect x="796.7" y="81" width="1.4" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (890 samples, 0.20%)')" onmouseout="c()">
<title>lapic_is_periodic (890 samples, 0.20%)</title><rect x="130.3" y="289" width="2.3" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_nhm_enable_all (93 samples, 0.02%)')" onmouseout="c()">
<title>intel_pmu_nhm_enable_all (93 samples, 0.02%)</title><rect x="1046.5" y="273" width="0.2" height="15.0" fill="rgb(208,11,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (1,699 samples, 0.38%)')" onmouseout="c()">
<title>irq_enter (1,699 samples, 0.38%)</title><rect x="345.2" y="321" width="4.5" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (2,638 samples, 0.59%)')" onmouseout="c()">
<title>irq_enter (2,638 samples, 0.59%)</title><rect x="1036.4" y="385" width="7.0" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_restart_sched_tick (563 samples, 0.13%)')" onmouseout="c()">
<title>tick_nohz_restart_sched_tick (563 samples, 0.13%)</title><rect x="1186.7" y="385" width="1.5" height="15.0" fill="rgb(210,23,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_match_dest (110 samples, 0.02%)')" onmouseout="c()">
<title>kvm_apic_match_dest (110 samples, 0.02%)</title><rect x="795.6" y="225" width="0.3" height="15.0" fill="rgb(236,199,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pm_qos_requirement (149 samples, 0.03%)')" onmouseout="c()">
<title>pm_qos_requirement (149 samples, 0.03%)</title><rect x="1105.0" y="401" width="0.4" height="15.0" fill="rgb(235,16,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (233 samples, 0.05%)')" onmouseout="c()">
<title>rb_erase (233 samples, 0.05%)</title><rect x="1137.4" y="385" width="0.7" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('place_entity (102 samples, 0.02%)')" onmouseout="c()">
<title>place_entity (102 samples, 0.02%)</title><rect x="1019.1" y="209" width="0.2" height="15.0" fill="rgb(226,35,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_writev (41 samples, 0.01%)')" onmouseout="c()">
<title>vfs_writev (41 samples, 0.01%)</title><rect x="982.9" y="417" width="0.1" height="15.0" fill="rgb(0,235,81)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (221 samples, 0.05%)')" onmouseout="c()">
<title>run_rebalance_domains (221 samples, 0.05%)</title><rect x="1184.5" y="257" width="0.6" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (230 samples, 0.05%)')" onmouseout="c()">
<title>native_sched_clock (230 samples, 0.05%)</title><rect x="679.9" y="241" width="0.6" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__switch_to (645 samples, 0.15%)')" onmouseout="c()">
<title>__switch_to (645 samples, 0.15%)</title><rect x="975.1" y="465" width="1.7" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (149 samples, 0.03%)')" onmouseout="c()">
<title>tick_program_event (149 samples, 0.03%)</title><rect x="1135.6" y="369" width="0.4" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_sched_out (230 samples, 0.05%)')" onmouseout="c()">
<title>perf_event_task_sched_out (230 samples, 0.05%)</title><rect x="1117.2" y="417" width="0.7" height="15.0" fill="rgb(215,151,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (118 samples, 0.03%)')" onmouseout="c()">
<title>tick_sched_timer (118 samples, 0.03%)</title><rect x="1126.7" y="353" width="0.3" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (100 samples, 0.02%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (100 samples, 0.02%)</title><rect x="574.5" y="305" width="0.3" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (43 samples, 0.01%)')" onmouseout="c()">
<title>rb_erase (43 samples, 0.01%)</title><rect x="1030.6" y="353" width="0.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_get_next_event (119 samples, 0.03%)')" onmouseout="c()">
<title>hrtimer_get_next_event (119 samples, 0.03%)</title><rect x="1154.1" y="417" width="0.3" height="15.0" fill="rgb(234,169,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_put (1,118 samples, 0.25%)')" onmouseout="c()">
<title>vmx_vcpu_put (1,118 samples, 0.25%)</title><rect x="685.5" y="289" width="3.0" height="15.0" fill="rgb(225,213,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__dequeue_entity (89 samples, 0.02%)')" onmouseout="c()">
<title>__dequeue_entity (89 samples, 0.02%)</title><rect x="1119.0" y="401" width="0.3" height="15.0" fill="rgb(254,73,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (39 samples, 0.01%)')" onmouseout="c()">
<title>tick_program_event (39 samples, 0.01%)</title><rect x="1187.0" y="305" width="0.1" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (63 samples, 0.01%)')" onmouseout="c()">
<title>[unknown] (63 samples, 0.01%)</title><rect x="1171.9" y="417" width="0.2" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_select (116 samples, 0.03%)')" onmouseout="c()">
<title>sys_select (116 samples, 0.03%)</title><rect x="970.5" y="417" width="0.3" height="15.0" fill="rgb(0,218,86)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_entity (72 samples, 0.02%)')" onmouseout="c()">
<title>dequeue_entity (72 samples, 0.02%)</title><rect x="627.3" y="273" width="0.2" height="15.0" fill="rgb(208,108,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (3,219 samples, 0.73%)')" onmouseout="c()">
<title>enqueue_hrtimer (3,219 samples, 0.73%)</title><rect x="805.7" y="193" width="8.5" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('eventfd_signal (76 samples, 0.02%)')" onmouseout="c()">
<title>eventfd_signal (76 samples, 0.02%)</title><rect x="766.7" y="257" width="0.2" height="15.0" fill="rgb(0,214,164)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_real (583 samples, 0.13%)')" onmouseout="c()">
<title>ktime_get_real (583 samples, 0.13%)</title><rect x="1100.7" y="417" width="1.6" height="15.0" fill="rgb(235,131,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (102 samples, 0.02%)')" onmouseout="c()">
<title>find_busiest_group (102 samples, 0.02%)</title><rect x="391.8" y="225" width="0.3" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_pwritev (41 samples, 0.01%)')" onmouseout="c()">
<title>sys_pwritev (41 samples, 0.01%)</title><rect x="982.9" y="433" width="0.1" height="15.0" fill="rgb(0,222,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_process_times (104 samples, 0.02%)')" onmouseout="c()">
<title>update_process_times (104 samples, 0.02%)</title><rect x="1126.8" y="337" width="0.2" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn_instantiation (39 samples, 0.01%)')" onmouseout="c()">
<title>unalias_gfn_instantiation (39 samples, 0.01%)</title><rect x="691.7" y="289" width="0.1" height="15.0" fill="rgb(212,78,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__switch_to (709 samples, 0.16%)')" onmouseout="c()">
<title>__switch_to (709 samples, 0.16%)</title><rect x="11.5" y="433" width="1.9" height="15.0" fill="rgb(218,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_idle (298 samples, 0.07%)')" onmouseout="c()">
<title>exit_idle (298 samples, 0.07%)</title><rect x="992.3" y="385" width="0.8" height="15.0" fill="rgb(217,85,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_futex (54 samples, 0.01%)')" onmouseout="c()">
<title>do_futex (54 samples, 0.01%)</title><rect x="983.5" y="417" width="0.1" height="15.0" fill="rgb(223,180,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_64_start_kernel (6,443 samples, 1.45%)')" onmouseout="c()">
<title>x86_64_start_kernel (6,443 samples, 1.45%)</title><rect x="1172.9" y="465" width="17.1" height="15.0" fill="rgb(230,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (463 samples, 0.10%)')" onmouseout="c()">
<title>run_rebalance_domains (463 samples, 0.10%)</title><rect x="391.5" y="257" width="1.2" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (387 samples, 0.09%)')" onmouseout="c()">
<title>tick_program_event (387 samples, 0.09%)</title><rect x="1133.1" y="353" width="1.0" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (1,061 samples, 0.24%)')" onmouseout="c()">
<title>ktime_get (1,061 samples, 0.24%)</title><rect x="339.2" y="273" width="2.8" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_next_timer_interrupt (556 samples, 0.13%)')" onmouseout="c()">
<title>get_next_timer_interrupt (556 samples, 0.13%)</title><rect x="1152.6" y="417" width="1.5" height="15.0" fill="rgb(245,174,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_IRQ (249 samples, 0.06%)')" onmouseout="c()">
<title>do_IRQ (249 samples, 0.06%)</title><rect x="914.4" y="337" width="0.7" height="15.0" fill="rgb(219,133,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (81 samples, 0.02%)')" onmouseout="c()">
<title>notifier_call_chain (81 samples, 0.02%)</title><rect x="992.8" y="353" width="0.2" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_has_pending_timer (839 samples, 0.19%)')" onmouseout="c()">
<title>apic_has_pending_timer (839 samples, 0.19%)</title><rect x="493.7" y="337" width="2.2" height="15.0" fill="rgb(248,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_stats_wait_end (104 samples, 0.02%)')" onmouseout="c()">
<title>update_stats_wait_end (104 samples, 0.02%)</title><rect x="1124.3" y="401" width="0.2" height="15.0" fill="rgb(253,139,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sem_post (62 samples, 0.01%)')" onmouseout="c()">
<title>sem_post (62 samples, 0.01%)</title><rect x="983.4" y="465" width="0.2" height="15.0" fill="rgb(221,97,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest_cached (345 samples, 0.08%)')" onmouseout="c()">
<title>kvm_write_guest_cached (345 samples, 0.08%)</title><rect x="573.9" y="321" width="0.9" height="15.0" fill="rgb(227,3,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_aio_write (38 samples, 0.01%)')" onmouseout="c()">
<title>generic_file_aio_write (38 samples, 0.01%)</title><rect x="982.9" y="353" width="0.1" height="15.0" fill="rgb(233,76,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_kick (918 samples, 0.21%)')" onmouseout="c()">
<title>kvm_vcpu_kick (918 samples, 0.21%)</title><rect x="796.4" y="193" width="2.4" height="15.0" fill="rgb(217,38,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_enable (100 samples, 0.02%)')" onmouseout="c()">
<title>__perf_event_enable (100 samples, 0.02%)</title><rect x="1046.5" y="353" width="0.2" height="15.0" fill="rgb(247,179,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_guest_get_msrs (621 samples, 0.14%)')" onmouseout="c()">
<title>intel_guest_get_msrs (621 samples, 0.14%)</title><rect x="880.9" y="321" width="1.6" height="15.0" fill="rgb(223,196,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (13,670 samples, 3.08%)')" onmouseout="c()">
<title>tick_program_event (13,670 samples, 3.08%)</title><rect x="307.2" y="305" width="36.3" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
<text text-anchor="" x="310.184003964098" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tic..</text>
</g>
<g class="func_g" onmouseover="s('handle_wrmsr (394 samples, 0.09%)')" onmouseout="c()">
<title>handle_wrmsr (394 samples, 0.09%)</title><rect x="500.4" y="337" width="1.0" height="15.0" fill="rgb(219,72,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_process_callbacks (7,055 samples, 1.59%)')" onmouseout="c()">
<title>__rcu_process_callbacks (7,055 samples, 1.59%)</title><rect x="369.4" y="241" width="18.7" height="15.0" fill="rgb(211,185,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_munmap (65 samples, 0.01%)')" onmouseout="c()">
<title>sys_munmap (65 samples, 0.01%)</title><rect x="973.4" y="433" width="0.1" height="15.0" fill="rgb(229,42,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (307 samples, 0.07%)')" onmouseout="c()">
<title>finish_task_switch (307 samples, 0.07%)</title><rect x="670.0" y="305" width="0.9" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_system_time (406 samples, 0.09%)')" onmouseout="c()">
<title>account_system_time (406 samples, 0.09%)</title><rect x="164.1" y="257" width="1.0" height="15.0" fill="rgb(248,96,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (214 samples, 0.05%)')" onmouseout="c()">
<title>finish_task_switch (214 samples, 0.05%)</title><rect x="1127.8" y="417" width="0.5" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_waking_fair (235 samples, 0.05%)')" onmouseout="c()">
<title>task_waking_fair (235 samples, 0.05%)</title><rect x="1027.5" y="257" width="0.6" height="15.0" fill="rgb(252,81,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (99 samples, 0.02%)')" onmouseout="c()">
<title>run_timer_softirq (99 samples, 0.02%)</title><rect x="1044.1" y="321" width="0.3" height="15.0" fill="rgb(219,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_unlock (706 samples, 0.16%)')" onmouseout="c()">
<title>srcu_read_unlock (706 samples, 0.16%)</title><rect x="718.5" y="337" width="1.9" height="15.0" fill="rgb(241,146,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (482 samples, 0.11%)')" onmouseout="c()">
<title>update_cfs_shares (482 samples, 0.11%)</title><rect x="677.9" y="289" width="1.3" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (369 samples, 0.08%)')" onmouseout="c()">
<title>__rb_rotate_left (369 samples, 0.08%)</title><rect x="124.7" y="257" width="1.0" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_has_pending_timer (1,589 samples, 0.36%)')" onmouseout="c()">
<title>apic_has_pending_timer (1,589 samples, 0.36%)</title><rect x="532.6" y="321" width="4.2" height="15.0" fill="rgb(248,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_memslot (110 samples, 0.02%)')" onmouseout="c()">
<title>gfn_to_memslot (110 samples, 0.02%)</title><rect x="691.8" y="305" width="0.3" height="15.0" fill="rgb(239,135,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (829 samples, 0.19%)')" onmouseout="c()">
<title>update_curr (829 samples, 0.19%)</title><rect x="1016.9" y="193" width="2.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (41 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (41 samples, 0.01%)</title><rect x="1167.8" y="401" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (43 samples, 0.01%)')" onmouseout="c()">
<title>rb_insert_color (43 samples, 0.01%)</title><rect x="1145.2" y="385" width="0.1" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_shared_msr (283 samples, 0.06%)')" onmouseout="c()">
<title>kvm_set_shared_msr (283 samples, 0.06%)</title><rect x="836.8" y="321" width="0.7" height="15.0" fill="rgb(252,70,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (117 samples, 0.03%)')" onmouseout="c()">
<title>system_call_fastpath (117 samples, 0.03%)</title><rect x="970.5" y="433" width="0.3" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_thread (171 samples, 0.04%)')" onmouseout="c()">
<title>start_thread (171 samples, 0.04%)</title><rect x="1171.9" y="465" width="0.5" height="15.0" fill="rgb(227,62,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_x2apic_msr_write (572 samples, 0.13%)')" onmouseout="c()">
<title>kvm_x2apic_msr_write (572 samples, 0.13%)</title><rect x="822.6" y="289" width="1.5" height="15.0" fill="rgb(227,74,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sync_read (69 samples, 0.02%)')" onmouseout="c()">
<title>do_sync_read (69 samples, 0.02%)</title><rect x="11.0" y="353" width="0.2" height="15.0" fill="rgb(0,217,139)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (85 samples, 0.02%)')" onmouseout="c()">
<title>__wake_up (85 samples, 0.02%)</title><rect x="400.8" y="193" width="0.3" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (1,378 samples, 0.31%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (1,378 samples, 0.31%)</title><rect x="1173.4" y="353" width="3.6" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_external_interrupt (571 samples, 0.13%)')" onmouseout="c()">
<title>handle_external_interrupt (571 samples, 0.13%)</title><rect x="764.2" y="321" width="1.6" height="15.0" fill="rgb(218,128,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (777 samples, 0.18%)')" onmouseout="c()">
<title>default_wake_function (777 samples, 0.18%)</title><rect x="796.5" y="129" width="2.1" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___libc_open (98 samples, 0.02%)')" onmouseout="c()">
<title>__GI___libc_open (98 samples, 0.02%)</title><rect x="971.9" y="465" width="0.3" height="15.0" fill="rgb(247,135,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_check_callbacks (2,146 samples, 0.48%)')" onmouseout="c()">
<title>rcu_check_callbacks (2,146 samples, 0.48%)</title><rect x="170.0" y="257" width="5.7" height="15.0" fill="rgb(244,188,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (199 samples, 0.04%)')" onmouseout="c()">
<title>__rb_rotate_left (199 samples, 0.04%)</title><rect x="120.1" y="273" width="0.6" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_first_bit (112 samples, 0.03%)')" onmouseout="c()">
<title>find_first_bit (112 samples, 0.03%)</title><rect x="669.7" y="305" width="0.3" height="15.0" fill="rgb(218,191,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_sched_out (50 samples, 0.01%)')" onmouseout="c()">
<title>perf_event_task_sched_out (50 samples, 0.01%)</title><rect x="612.0" y="321" width="0.1" height="15.0" fill="rgb(215,151,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (94 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (94 samples, 0.02%)</title><rect x="1133.8" y="321" width="0.3" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_wait (100 samples, 0.02%)')" onmouseout="c()">
<title>finish_wait (100 samples, 0.02%)</title><rect x="497.7" y="337" width="0.3" height="15.0" fill="rgb(215,130,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (825 samples, 0.19%)')" onmouseout="c()">
<title>lock_hrtimer_base (825 samples, 0.19%)</title><rect x="789.7" y="209" width="2.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('poll_schedule_timeout (40 samples, 0.01%)')" onmouseout="c()">
<title>poll_schedule_timeout (40 samples, 0.01%)</title><rect x="970.6" y="369" width="0.1" height="15.0" fill="rgb(0,215,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('generic_file_aio_read (67 samples, 0.02%)')" onmouseout="c()">
<title>generic_file_aio_read (67 samples, 0.02%)</title><rect x="11.0" y="337" width="0.2" height="15.0" fill="rgb(0,204,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (145 samples, 0.03%)')" onmouseout="c()">
<title>sched_clock_cpu (145 samples, 0.03%)</title><rect x="1169.7" y="401" width="0.4" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_idle_sibling (166 samples, 0.04%)')" onmouseout="c()">
<title>select_idle_sibling (166 samples, 0.04%)</title><rect x="1026.0" y="241" width="0.4" height="15.0" fill="rgb(233,205,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_shares (233 samples, 0.05%)')" onmouseout="c()">
<title>update_shares (233 samples, 0.05%)</title><rect x="392.1" y="225" width="0.6" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('napi_gro_receive (57 samples, 0.01%)')" onmouseout="c()">
<title>napi_gro_receive (57 samples, 0.01%)</title><rect x="914.7" y="209" width="0.2" height="15.0" fill="rgb(219,67,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (1,381 samples, 0.31%)')" onmouseout="c()">
<title>pit_has_pending_timer (1,381 samples, 0.31%)</title><rect x="704.2" y="337" width="3.7" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_find_highest_irr (743 samples, 0.17%)')" onmouseout="c()">
<title>kvm_lapic_find_highest_irr (743 samples, 0.17%)</title><rect x="556.7" y="337" width="2.0" height="15.0" fill="rgb(217,222,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (123 samples, 0.03%)')" onmouseout="c()">
<title>ktime_get (123 samples, 0.03%)</title><rect x="1035.8" y="337" width="0.4" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (100 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (100 samples, 0.02%)</title><rect x="1148.3" y="417" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (4,939 samples, 1.11%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (4,939 samples, 1.11%)</title><rect x="1154.4" y="401" width="13.1" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (242 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_interrupt (242 samples, 0.05%)</title><rect x="1126.5" y="385" width="0.6" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (71,520 samples, 16.11%)')" onmouseout="c()">
<title>__run_hrtimer (71,520 samples, 16.11%)</title><rect x="101.2" y="305" width="190.0" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
<text text-anchor="" x="104.155421917407" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__run_hrtimer</text>
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (2,967 samples, 0.67%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (2,967 samples, 0.67%)</title><rect x="1138.5" y="401" width="7.9" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (85 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (85 samples, 0.02%)</title><rect x="998.3" y="337" width="0.3" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('wake_up_state (45 samples, 0.01%)')" onmouseout="c()">
<title>wake_up_state (45 samples, 0.01%)</title><rect x="983.5" y="369" width="0.1" height="15.0" fill="rgb(0,211,188)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (103 samples, 0.02%)')" onmouseout="c()">
<title>[unknown] (103 samples, 0.02%)</title><rect x="11.0" y="433" width="0.3" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_enter (293 samples, 0.07%)')" onmouseout="c()">
<title>rcu_irq_enter (293 samples, 0.07%)</title><rect x="411.5" y="321" width="0.8" height="15.0" fill="rgb(233,147,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (149 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (149 samples, 0.03%)</title><rect x="817.0" y="209" width="0.4" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__apic_accept_irq (467 samples, 0.11%)')" onmouseout="c()">
<title>__apic_accept_irq (467 samples, 0.11%)</title><rect x="543.7" y="305" width="1.2" height="15.0" fill="rgb(223,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_reg_read (39 samples, 0.01%)')" onmouseout="c()">
<title>proc_reg_read (39 samples, 0.01%)</title><rect x="972.4" y="401" width="0.1" height="15.0" fill="rgb(0,229,112)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_stop (189 samples, 0.04%)')" onmouseout="c()">
<title>pick_next_task_stop (189 samples, 0.04%)</title><rect x="612.6" y="321" width="0.5" height="15.0" fill="rgb(220,169,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_tick (43 samples, 0.01%)')" onmouseout="c()">
<title>scheduler_tick (43 samples, 0.01%)</title><rect x="1126.9" y="321" width="0.1" height="15.0" fill="rgb(240,30,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (542 samples, 0.12%)')" onmouseout="c()">
<title>native_write_msr_safe (542 samples, 0.12%)</title><rect x="211.7" y="145" width="1.4" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_get_next_event (205 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_get_next_event (205 samples, 0.05%)</title><rect x="1153.5" y="401" width="0.6" height="15.0" fill="rgb(234,169,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_futex (55 samples, 0.01%)')" onmouseout="c()">
<title>sys_futex (55 samples, 0.01%)</title><rect x="983.5" y="433" width="0.1" height="15.0" fill="rgb(236,194,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (128 samples, 0.03%)')" onmouseout="c()">
<title>vmcs_writel (128 samples, 0.03%)</title><rect x="839.6" y="321" width="0.3" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_get_apic_interrupt (657 samples, 0.15%)')" onmouseout="c()">
<title>kvm_get_apic_interrupt (657 samples, 0.15%)</title><rect x="538.8" y="337" width="1.8" height="15.0" fill="rgb(252,74,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__generic_file_aio_write (345 samples, 0.08%)')" onmouseout="c()">
<title>__generic_file_aio_write (345 samples, 0.08%)</title><rect x="974.0" y="305" width="0.9" height="15.0" fill="rgb(240,68,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_next_entity (129 samples, 0.03%)')" onmouseout="c()">
<title>set_next_entity (129 samples, 0.03%)</title><rect x="1186.2" y="353" width="0.3" height="15.0" fill="rgb(232,112,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn_instantiation (186 samples, 0.04%)')" onmouseout="c()">
<title>unalias_gfn_instantiation (186 samples, 0.04%)</title><rect x="596.6" y="305" width="0.5" height="15.0" fill="rgb(212,78,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_apic_timer (8,421 samples, 1.90%)')" onmouseout="c()">
<title>start_apic_timer (8,421 samples, 1.90%)</title><rect x="798.8" y="241" width="22.4" height="15.0" fill="rgb(251,224,41)" rx="2" ry="2" />
<text text-anchor="" x="801.835388582948" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s('clear_buddies (47 samples, 0.01%)')" onmouseout="c()">
<title>clear_buddies (47 samples, 0.01%)</title><rect x="631.4" y="241" width="0.1" height="15.0" fill="rgb(216,188,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (42 samples, 0.01%)')" onmouseout="c()">
<title>do_softirq (42 samples, 0.01%)</title><rect x="1176.8" y="321" width="0.1" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_fair (39 samples, 0.01%)')" onmouseout="c()">
<title>put_prev_task_fair (39 samples, 0.01%)</title><rect x="767.5" y="257" width="0.1" height="15.0" fill="rgb(238,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (174 samples, 0.04%)')" onmouseout="c()">
<title>apic_update_ppr (174 samples, 0.04%)</title><rect x="608.1" y="273" width="0.5" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (114 samples, 0.03%)')" onmouseout="c()">
<title>rb_insert_color (114 samples, 0.03%)</title><rect x="1144.6" y="369" width="0.3" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task_fair (7,840 samples, 1.77%)')" onmouseout="c()">
<title>dequeue_task_fair (7,840 samples, 1.77%)</title><rect x="627.5" y="273" width="20.9" height="15.0" fill="rgb(205,43,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (53 samples, 0.01%)')" onmouseout="c()">
<title>thread_return (53 samples, 0.01%)</title><rect x="1186.6" y="385" width="0.1" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (132 samples, 0.03%)')" onmouseout="c()">
<title>run_rebalance_domains (132 samples, 0.03%)</title><rect x="1127.3" y="321" width="0.4" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (103 samples, 0.02%)')" onmouseout="c()">
<title>autoremove_wake_function (103 samples, 0.02%)</title><rect x="1028.8" y="321" width="0.3" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (730 samples, 0.16%)')" onmouseout="c()">
<title>apic_update_ppr (730 samples, 0.16%)</title><rect x="517.7" y="321" width="1.9" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (136 samples, 0.03%)')" onmouseout="c()">
<title>gfn_to_hva (136 samples, 0.03%)</title><rect x="691.4" y="305" width="0.4" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_ioctl (348,207 samples, 78.43%)')" onmouseout="c()">
<title>kvm_vcpu_ioctl (348,207 samples, 78.43%)</title><rect x="16.2" y="369" width="925.4" height="15.0" fill="rgb(251,164,31)" rx="2" ry="2" />
<text text-anchor="" x="19.17394731804" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_vcpu_ioctl</text>
</g>
<g class="func_g" onmouseover="s('schedule (86 samples, 0.02%)')" onmouseout="c()">
<title>schedule (86 samples, 0.02%)</title><rect x="15.9" y="433" width="0.3" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (68 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (68 samples, 0.02%)</title><rect x="1137.3" y="369" width="0.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty (309 samples, 0.07%)')" onmouseout="c()">
<title>mark_page_dirty (309 samples, 0.07%)</title><rect x="692.1" y="305" width="0.8" height="15.0" fill="rgb(223,37,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (45 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (45 samples, 0.01%)</title><rect x="153.0" y="225" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (127 samples, 0.03%)')" onmouseout="c()">
<title>idle_cpu (127 samples, 0.03%)</title><rect x="165.4" y="257" width="0.3" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up (11,139 samples, 2.51%)')" onmouseout="c()">
<title>__wake_up (11,139 samples, 2.51%)</title><rect x="999.5" y="337" width="29.6" height="15.0" fill="rgb(226,70,29)" rx="2" ry="2" />
<text text-anchor="" x="1002.51126727254" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s('proc_single_show (282 samples, 0.06%)')" onmouseout="c()">
<title>proc_single_show (282 samples, 0.06%)</title><rect x="972.5" y="385" width="0.7" height="15.0" fill="rgb(210,183,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (6,532 samples, 1.47%)')" onmouseout="c()">
<title>enqueue_task_fair (6,532 samples, 1.47%)</title><rect x="1003.3" y="225" width="17.3" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (41 samples, 0.01%)')" onmouseout="c()">
<title>sched_clock (41 samples, 0.01%)</title><rect x="679.3" y="273" width="0.1" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_idle (20,259 samples, 4.56%)')" onmouseout="c()">
<title>intel_idle (20,259 samples, 4.56%)</title><rect x="1046.9" y="417" width="53.8" height="15.0" fill="rgb(216,81,47)" rx="2" ry="2" />
<text text-anchor="" x="1049.85106478822" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >intel..</text>
</g>
<g class="func_g" onmouseover="s('vfs_write (349 samples, 0.08%)')" onmouseout="c()">
<title>vfs_write (349 samples, 0.08%)</title><rect x="974.0" y="369" width="0.9" height="15.0" fill="rgb(241,195,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (424 samples, 0.10%)')" onmouseout="c()">
<title>sched_clock_tick (424 samples, 0.10%)</title><rect x="216.5" y="241" width="1.1" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (40 samples, 0.01%)')" onmouseout="c()">
<title>native_sched_clock (40 samples, 0.01%)</title><rect x="679.7" y="257" width="0.1" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('prepare_to_wait (788 samples, 0.18%)')" onmouseout="c()">
<title>prepare_to_wait (788 samples, 0.18%)</title><rect x="613.9" y="321" width="2.0" height="15.0" fill="rgb(227,79,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (1,393 samples, 0.31%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (1,393 samples, 0.31%)</title><rect x="519.6" y="321" width="3.7" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (220 samples, 0.05%)')" onmouseout="c()">
<title>irq_exit (220 samples, 0.05%)</title><rect x="990.2" y="401" width="0.6" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_block (34,006 samples, 7.66%)')" onmouseout="c()">
<title>kvm_vcpu_block (34,006 samples, 7.66%)</title><rect x="599.6" y="337" width="90.4" height="15.0" fill="rgb(221,155,20)" rx="2" ry="2" />
<text text-anchor="" x="602.589378019528" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_vcpu_b..</text>
</g>
<g class="func_g" onmouseover="s('account_process_tick (430 samples, 0.10%)')" onmouseout="c()">
<title>account_process_tick (430 samples, 0.10%)</title><rect x="140.1" y="273" width="1.1" height="15.0" fill="rgb(250,142,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_reflect (105 samples, 0.02%)')" onmouseout="c()">
<title>menu_reflect (105 samples, 0.02%)</title><rect x="1112.0" y="433" width="0.3" height="15.0" fill="rgb(231,223,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (92 samples, 0.02%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (92 samples, 0.02%)</title><rect x="506.1" y="321" width="0.3" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (237 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_cancel (237 samples, 0.05%)</title><rect x="1186.8" y="369" width="0.6" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (51 samples, 0.01%)')" onmouseout="c()">
<title>__remove_hrtimer (51 samples, 0.01%)</title><rect x="1167.5" y="401" width="0.2" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (49 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (49 samples, 0.01%)</title><rect x="992.1" y="385" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (1,990 samples, 0.45%)')" onmouseout="c()">
<title>rb_erase (1,990 samples, 0.45%)</title><rect x="107.7" y="273" width="5.3" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('reschedule_interrupt (88 samples, 0.02%)')" onmouseout="c()">
<title>reschedule_interrupt (88 samples, 0.02%)</title><rect x="911.4" y="353" width="0.2" height="15.0" fill="rgb(213,96,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_secondary (70,776 samples, 15.94%)')" onmouseout="c()">
<title>start_secondary (70,776 samples, 15.94%)</title><rect x="983.8" y="465" width="188.1" height="15.0" fill="rgb(214,25,2)" rx="2" ry="2" />
<text text-anchor="" x="986.833215086095" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >start_secondary</text>
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (57 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (57 samples, 0.01%)</title><rect x="983.7" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (217 samples, 0.05%)')" onmouseout="c()">
<title>do_softirq (217 samples, 0.05%)</title><rect x="914.5" y="305" width="0.6" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmstat_update (50 samples, 0.01%)')" onmouseout="c()">
<title>vmstat_update (50 samples, 0.01%)</title><rect x="980.4" y="417" width="0.1" height="15.0" fill="rgb(252,71,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (54 samples, 0.01%)')" onmouseout="c()">
<title>read_tsc (54 samples, 0.01%)</title><rect x="1168.3" y="401" width="0.2" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (642 samples, 0.14%)')" onmouseout="c()">
<title>_spin_lock (642 samples, 0.14%)</title><rect x="201.1" y="209" width="1.7" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (331 samples, 0.07%)')" onmouseout="c()">
<title>rb_insert_color (331 samples, 0.07%)</title><rect x="134.3" y="289" width="0.8" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (1,201 samples, 0.27%)')" onmouseout="c()">
<title>tick_program_event (1,201 samples, 0.27%)</title><rect x="1157.9" y="353" width="3.1" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_local_timers (1,074 samples, 0.24%)')" onmouseout="c()">
<title>run_local_timers (1,074 samples, 0.24%)</title><rect x="175.7" y="257" width="2.9" height="15.0" fill="rgb(216,154,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (439 samples, 0.10%)')" onmouseout="c()">
<title>read_tsc (439 samples, 0.10%)</title><rect x="298.0" y="289" width="1.1" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enter_idle (251 samples, 0.06%)')" onmouseout="c()">
<title>enter_idle (251 samples, 0.06%)</title><rect x="1109.8" y="433" width="0.7" height="15.0" fill="rgb(237,51,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vcpu_put (1,928 samples, 0.43%)')" onmouseout="c()">
<title>vcpu_put (1,928 samples, 0.43%)</title><rect x="684.3" y="321" width="5.2" height="15.0" fill="rgb(238,142,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('all (443,985 samples, 100%)')" onmouseout="c()">
<title>all (443,985 samples, 100%)</title><rect x="10.0" y="481" width="1180.0" height="15.0" fill="rgb(213,209,9)" rx="2" ry="2" />
<text text-anchor="" x="13" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('irq_exit (79 samples, 0.02%)')" onmouseout="c()">
<title>irq_exit (79 samples, 0.02%)</title><rect x="1176.7" y="337" width="0.2" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_tick_fair (21,979 samples, 4.95%)')" onmouseout="c()">
<title>task_tick_fair (21,979 samples, 4.95%)</title><rect x="217.6" y="241" width="58.4" height="15.0" fill="rgb(231,38,9)" rx="2" ry="2" />
<text text-anchor="" x="220.578048807955" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >task_t..</text>
</g>
<g class="func_g" onmouseover="s('resched_task (71 samples, 0.02%)')" onmouseout="c()">
<title>resched_task (71 samples, 0.02%)</title><rect x="1023.8" y="257" width="0.2" height="15.0" fill="rgb(236,7,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_tx_kick (48 samples, 0.01%)')" onmouseout="c()">
<title>handle_tx_kick (48 samples, 0.01%)</title><rect x="977.2" y="401" width="0.1" height="15.0" fill="rgb(0,214,111)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (863 samples, 0.19%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (863 samples, 0.19%)</title><rect x="502.7" y="337" width="2.3" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (135 samples, 0.03%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (135 samples, 0.03%)</title><rect x="1143.5" y="385" width="0.4" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (975 samples, 0.22%)')" onmouseout="c()">
<title>native_apic_mem_write (975 samples, 0.22%)</title><rect x="408.9" y="321" width="2.6" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (71 samples, 0.02%)')" onmouseout="c()">
<title>native_read_tsc (71 samples, 0.02%)</title><rect x="144.0" y="241" width="0.2" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (138 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock (138 samples, 0.03%)</title><rect x="1040.2" y="337" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (96 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irq (96 samples, 0.02%)</title><rect x="618.3" y="305" width="0.2" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_idle (134 samples, 0.03%)')" onmouseout="c()">
<title>exit_idle (134 samples, 0.03%)</title><rect x="49.5" y="321" width="0.4" height="15.0" fill="rgb(217,85,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_set_msr_common (473 samples, 0.11%)')" onmouseout="c()">
<title>kvm_set_msr_common (473 samples, 0.11%)</title><rect x="769.0" y="305" width="1.3" height="15.0" fill="rgb(225,95,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (428 samples, 0.10%)')" onmouseout="c()">
<title>__remove_hrtimer (428 samples, 0.10%)</title><rect x="997.4" y="353" width="1.2" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_rq_lock (93 samples, 0.02%)')" onmouseout="c()">
<title>task_rq_lock (93 samples, 0.02%)</title><rect x="1000.6" y="273" width="0.2" height="15.0" fill="rgb(231,149,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (42 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (42 samples, 0.01%)</title><rect x="1136.0" y="385" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (137 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock (137 samples, 0.03%)</title><rect x="1027.1" y="241" width="0.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task (432 samples, 0.10%)')" onmouseout="c()">
<title>enqueue_task (432 samples, 0.10%)</title><rect x="1174.2" y="193" width="1.2" height="15.0" fill="rgb(225,196,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (56 samples, 0.01%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (56 samples, 0.01%)</title><rect x="626.0" y="289" width="0.2" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('place_entity (291 samples, 0.07%)')" onmouseout="c()">
<title>place_entity (291 samples, 0.07%)</title><rect x="1008.8" y="193" width="0.7" height="15.0" fill="rgb(226,35,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (94 samples, 0.02%)')" onmouseout="c()">
<title>clockevents_program_event (94 samples, 0.02%)</title><rect x="1133.6" y="321" width="0.2" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (54 samples, 0.01%)')" onmouseout="c()">
<title>pick_next_task_fair (54 samples, 0.01%)</title><rect x="1112.6" y="433" width="0.1" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (69 samples, 0.02%)')" onmouseout="c()">
<title>update_curr (69 samples, 0.02%)</title><rect x="797.7" y="33" width="0.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__write_nocancel (362 samples, 0.08%)')" onmouseout="c()">
<title>__write_nocancel (362 samples, 0.08%)</title><rect x="974.0" y="417" width="0.9" height="15.0" fill="rgb(231,158,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_futex (57 samples, 0.01%)')" onmouseout="c()">
<title>sys_futex (57 samples, 0.01%)</title><rect x="983.7" y="433" width="0.1" height="15.0" fill="rgb(236,194,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start_range_ns (68 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_start_range_ns (68 samples, 0.02%)</title><rect x="1111.3" y="433" width="0.2" height="15.0" fill="rgb(207,72,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (216 samples, 0.05%)')" onmouseout="c()">
<title>read_tsc (216 samples, 0.05%)</title><rect x="342.9" y="273" width="0.6" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (68 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_shares (68 samples, 0.02%)</title><rect x="392.5" y="209" width="0.2" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (151 samples, 0.03%)')" onmouseout="c()">
<title>__remove_hrtimer (151 samples, 0.03%)</title><rect x="1186.8" y="337" width="0.4" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('refresh_cpu_vm_stats (39 samples, 0.01%)')" onmouseout="c()">
<title>refresh_cpu_vm_stats (39 samples, 0.01%)</title><rect x="980.4" y="401" width="0.1" height="15.0" fill="rgb(0,236,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (81 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irqsave (81 samples, 0.02%)</title><rect x="601.5" y="321" width="0.3" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (511 samples, 0.12%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (511 samples, 0.12%)</title><rect x="513.1" y="305" width="1.3" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (3,284 samples, 0.74%)')" onmouseout="c()">
<title>hrtimer_cancel (3,284 samples, 0.74%)</title><rect x="1129.6" y="417" width="8.7" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtick_start_fair (51 samples, 0.01%)')" onmouseout="c()">
<title>hrtick_start_fair (51 samples, 0.01%)</title><rect x="648.4" y="273" width="0.1" height="15.0" fill="rgb(209,42,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (645 samples, 0.15%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (645 samples, 0.15%)</title><rect x="606.9" y="289" width="1.7" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_disable (388 samples, 0.09%)')" onmouseout="c()">
<title>perf_pmu_disable (388 samples, 0.09%)</title><rect x="215.1" y="225" width="1.1" height="15.0" fill="rgb(218,159,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (265 samples, 0.06%)')" onmouseout="c()">
<title>__rb_rotate_right (265 samples, 0.06%)</title><rect x="813.5" y="161" width="0.7" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (146 samples, 0.03%)')" onmouseout="c()">
<title>tick_program_event (146 samples, 0.03%)</title><rect x="1147.7" y="401" width="0.4" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mem_cgroup_charge_common (175 samples, 0.04%)')" onmouseout="c()">
<title>mem_cgroup_charge_common (175 samples, 0.04%)</title><rect x="762.2" y="145" width="0.5" height="15.0" fill="rgb(0,215,179)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (321 samples, 0.07%)')" onmouseout="c()">
<title>rb_erase (321 samples, 0.07%)</title><rect x="1121.5" y="369" width="0.9" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_for_new_grace_period (122 samples, 0.03%)')" onmouseout="c()">
<title>check_for_new_grace_period (122 samples, 0.03%)</title><rect x="389.1" y="241" width="0.3" height="15.0" fill="rgb(217,224,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (57 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock_irqsave (57 samples, 0.01%)</title><rect x="1145.0" y="369" width="0.1" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('profile_tick (256 samples, 0.06%)')" onmouseout="c()">
<title>profile_tick (256 samples, 0.06%)</title><rect x="132.7" y="289" width="0.7" height="15.0" fill="rgb(220,196,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (1,467 samples, 0.33%)')" onmouseout="c()">
<title>apic_timer_interrupt (1,467 samples, 0.33%)</title><rect x="1173.1" y="369" width="3.9" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('blk_end_request (39 samples, 0.01%)')" onmouseout="c()">
<title>blk_end_request (39 samples, 0.01%)</title><rect x="914.5" y="193" width="0.2" height="15.0" fill="rgb(0,238,151)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('path_walk (39 samples, 0.01%)')" onmouseout="c()">
<title>path_walk (39 samples, 0.01%)</title><rect x="971.9" y="369" width="0.1" height="15.0" fill="rgb(244,163,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (153,285 samples, 34.52%)')" onmouseout="c()">
<title>apic_timer_interrupt (153,285 samples, 34.52%)</title><rect x="16.2" y="353" width="407.4" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
<text text-anchor="" x="19.1766050654864" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >apic_timer_interrupt</text>
</g>
<g class="func_g" onmouseover="s('fix_small_imbalance (107 samples, 0.02%)')" onmouseout="c()">
<title>fix_small_imbalance (107 samples, 0.02%)</title><rect x="668.6" y="289" width="0.3" height="15.0" fill="rgb(206,210,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_on_spin (275 samples, 0.06%)')" onmouseout="c()">
<title>kvm_vcpu_on_spin (275 samples, 0.06%)</title><rect x="766.9" y="305" width="0.7" height="15.0" fill="rgb(211,105,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (720 samples, 0.16%)')" onmouseout="c()">
<title>__do_softirq (720 samples, 0.16%)</title><rect x="1106.7" y="321" width="2.0" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (1,916 samples, 0.43%)')" onmouseout="c()">
<title>ktime_get_update_offsets (1,916 samples, 0.43%)</title><rect x="294.0" y="305" width="5.1" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_cond_resched (76 samples, 0.02%)')" onmouseout="c()">
<title>_cond_resched (76 samples, 0.02%)</title><rect x="680.7" y="305" width="0.2" height="15.0" fill="rgb(243,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_disable (701 samples, 0.16%)')" onmouseout="c()">
<title>x86_pmu_disable (701 samples, 0.16%)</title><rect x="213.2" y="209" width="1.9" height="15.0" fill="rgb(223,177,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (57 samples, 0.01%)')" onmouseout="c()">
<title>pick_next_task_fair (57 samples, 0.01%)</title><rect x="612.1" y="321" width="0.1" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_bit (196 samples, 0.04%)')" onmouseout="c()">
<title>find_next_bit (196 samples, 0.04%)</title><rect x="668.1" y="289" width="0.5" height="15.0" fill="rgb(231,134,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_get_cr8 (306 samples, 0.07%)')" onmouseout="c()">
<title>kvm_lapic_get_cr8 (306 samples, 0.07%)</title><rect x="726.8" y="321" width="0.9" height="15.0" fill="rgb(234,219,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (97 samples, 0.02%)')" onmouseout="c()">
<title>tick_dev_program_event (97 samples, 0.02%)</title><rect x="1176.1" y="305" width="0.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_select (98 samples, 0.02%)')" onmouseout="c()">
<title>do_select (98 samples, 0.02%)</title><rect x="970.5" y="385" width="0.3" height="15.0" fill="rgb(0,223,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (258 samples, 0.06%)')" onmouseout="c()">
<title>_spin_lock_irqsave (258 samples, 0.06%)</title><rect x="388.1" y="241" width="0.7" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_queues (90 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_run_queues (90 samples, 0.02%)</title><rect x="165.2" y="257" width="0.2" height="15.0" fill="rgb(250,176,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (136 samples, 0.03%)')" onmouseout="c()">
<title>read_tsc (136 samples, 0.03%)</title><rect x="820.4" y="209" width="0.3" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (106 samples, 0.02%)')" onmouseout="c()">
<title>read_tsc (106 samples, 0.02%)</title><rect x="1099.6" y="369" width="0.3" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_find_highest_irr (169 samples, 0.04%)')" onmouseout="c()">
<title>kvm_lapic_find_highest_irr (169 samples, 0.04%)</title><rect x="726.4" y="321" width="0.4" height="15.0" fill="rgb(217,222,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (79 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (79 samples, 0.02%)</title><rect x="648.8" y="257" width="0.2" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (197 samples, 0.04%)')" onmouseout="c()">
<title>tick_program_event (197 samples, 0.04%)</title><rect x="1045.9" y="385" width="0.5" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (488 samples, 0.11%)')" onmouseout="c()">
<title>idle_cpu (488 samples, 0.11%)</title><rect x="343.9" y="321" width="1.3" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (440 samples, 0.10%)')" onmouseout="c()">
<title>rb_erase (440 samples, 0.10%)</title><rect x="1134.1" y="369" width="1.2" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_64_start_reservations (6,443 samples, 1.45%)')" onmouseout="c()">
<title>x86_64_start_reservations (6,443 samples, 1.45%)</title><rect x="1172.9" y="449" width="17.1" height="15.0" fill="rgb(215,162,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_pfn (138 samples, 0.03%)')" onmouseout="c()">
<title>gfn_to_pfn (138 samples, 0.03%)</title><rect x="756.1" y="273" width="0.4" height="15.0" fill="rgb(0,237,55)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__link_path_walk (38 samples, 0.01%)')" onmouseout="c()">
<title>__link_path_walk (38 samples, 0.01%)</title><rect x="971.9" y="353" width="0.1" height="15.0" fill="rgb(241,49,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___ioctl (360,742 samples, 81.25%)')" onmouseout="c()">
<title>__GI___ioctl (360,742 samples, 81.25%)</title><rect x="11.5" y="449" width="958.8" height="15.0" fill="rgb(227,98,38)" rx="2" ry="2" />
<text text-anchor="" x="14.4936540648896" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__GI___ioctl</text>
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_put (124 samples, 0.03%)')" onmouseout="c()">
<title>kvm_arch_vcpu_put (124 samples, 0.03%)</title><rect x="604.3" y="321" width="0.4" height="15.0" fill="rgb(243,89,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (74 samples, 0.02%)')" onmouseout="c()">
<title>calc_delta_mine (74 samples, 0.02%)</title><rect x="639.7" y="225" width="0.2" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (433 samples, 0.10%)')" onmouseout="c()">
<title>native_read_tsc (433 samples, 0.10%)</title><rect x="744.3" y="321" width="1.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (188 samples, 0.04%)')" onmouseout="c()">
<title>idle_cpu (188 samples, 0.04%)</title><rect x="1037.1" y="369" width="0.5" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_set_irq (1,005 samples, 0.23%)')" onmouseout="c()">
<title>kvm_apic_set_irq (1,005 samples, 0.23%)</title><rect x="796.2" y="225" width="2.6" height="15.0" fill="rgb(231,19,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (99 samples, 0.02%)')" onmouseout="c()">
<title>tick_dev_program_event (99 samples, 0.02%)</title><rect x="1157.6" y="353" width="0.3" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_has_interrupt (92 samples, 0.02%)')" onmouseout="c()">
<title>kvm_apic_has_interrupt (92 samples, 0.02%)</title><rect x="605.4" y="305" width="0.2" height="15.0" fill="rgb(217,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (215 samples, 0.05%)')" onmouseout="c()">
<title>__do_softirq (215 samples, 0.05%)</title><rect x="914.5" y="273" width="0.6" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_guest_get_msrs (929 samples, 0.21%)')" onmouseout="c()">
<title>perf_guest_get_msrs (929 samples, 0.21%)</title><rect x="701.8" y="337" width="2.4" height="15.0" fill="rgb(219,77,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_page (604 samples, 0.14%)')" onmouseout="c()">
<title>kvm_read_guest_page (604 samples, 0.14%)</title><rect x="595.5" y="321" width="1.6" height="15.0" fill="rgb(221,162,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (78 samples, 0.02%)')" onmouseout="c()">
<title>__list_add (78 samples, 0.02%)</title><rect x="1007.3" y="193" width="0.2" height="15.0" fill="rgb(223,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_halt (104 samples, 0.02%)')" onmouseout="c()">
<title>handle_halt (104 samples, 0.02%)</title><rect x="500.1" y="337" width="0.3" height="15.0" fill="rgb(226,182,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('physflat_send_IPI_mask (86 samples, 0.02%)')" onmouseout="c()">
<title>physflat_send_IPI_mask (86 samples, 0.02%)</title><rect x="191.8" y="225" width="0.3" height="15.0" fill="rgb(224,172,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (127 samples, 0.03%)')" onmouseout="c()">
<title>thread_return (127 samples, 0.03%)</title><rect x="1172.4" y="465" width="0.4" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ret_from_intr (1,043 samples, 0.23%)')" onmouseout="c()">
<title>ret_from_intr (1,043 samples, 0.23%)</title><rect x="912.3" y="353" width="2.8" height="15.0" fill="rgb(237,83,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_vmas (41 samples, 0.01%)')" onmouseout="c()">
<title>unmap_vmas (41 samples, 0.01%)</title><rect x="980.6" y="401" width="0.1" height="15.0" fill="rgb(217,28,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (132 samples, 0.03%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (132 samples, 0.03%)</title><rect x="225.4" y="225" width="0.3" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (272 samples, 0.06%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (272 samples, 0.06%)</title><rect x="832.5" y="337" width="0.7" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('blk_end_bidi_request (39 samples, 0.01%)')" onmouseout="c()">
<title>blk_end_bidi_request (39 samples, 0.01%)</title><rect x="914.5" y="177" width="0.2" height="15.0" fill="rgb(0,239,67)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('core_sys_select (109 samples, 0.02%)')" onmouseout="c()">
<title>core_sys_select (109 samples, 0.02%)</title><rect x="970.5" y="401" width="0.3" height="15.0" fill="rgb(0,200,99)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_timer_softirq (543 samples, 0.12%)')" onmouseout="c()">
<title>run_timer_softirq (543 samples, 0.12%)</title><rect x="404.8" y="273" width="1.4" height="15.0" fill="rgb(219,191,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_load (330 samples, 0.07%)')" onmouseout="c()">
<title>vmx_vcpu_load (330 samples, 0.07%)</title><rect x="683.5" y="305" width="0.8" height="15.0" fill="rgb(243,164,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (49 samples, 0.01%)')" onmouseout="c()">
<title>vmcs_writel (49 samples, 0.01%)</title><rect x="766.2" y="289" width="0.1" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_open (95 samples, 0.02%)')" onmouseout="c()">
<title>sys_open (95 samples, 0.02%)</title><rect x="971.9" y="433" width="0.3" height="15.0" fill="rgb(246,217,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (198 samples, 0.04%)')" onmouseout="c()">
<title>__rb_rotate_left (198 samples, 0.04%)</title><rect x="808.6" y="177" width="0.5" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (97 samples, 0.02%)')" onmouseout="c()">
<title>[unknown] (97 samples, 0.02%)</title><rect x="11.0" y="417" width="0.2" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (99 samples, 0.02%)')" onmouseout="c()">
<title>update_rq_clock (99 samples, 0.02%)</title><rect x="650.3" y="289" width="0.2" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_pending_timer (1,212 samples, 0.27%)')" onmouseout="c()">
<title>kvm_cpu_has_pending_timer (1,212 samples, 0.27%)</title><rect x="895.3" y="353" width="3.3" height="15.0" fill="rgb(221,78,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_tick (8,825 samples, 1.99%)')" onmouseout="c()">
<title>perf_event_task_tick (8,825 samples, 1.99%)</title><rect x="192.8" y="241" width="23.5" height="15.0" fill="rgb(224,223,5)" rx="2" ry="2" />
<text text-anchor="" x="195.839735576652" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (1,222 samples, 0.28%)')" onmouseout="c()">
<title>native_apic_mem_write (1,222 samples, 0.28%)</title><rect x="38.7" y="337" width="3.3" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (99 samples, 0.02%)')" onmouseout="c()">
<title>native_sched_clock (99 samples, 0.02%)</title><rect x="649.3" y="241" width="0.2" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (348,207 samples, 78.43%)')" onmouseout="c()">
<title>system_call_fastpath (348,207 samples, 78.43%)</title><rect x="16.2" y="433" width="925.4" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
<text text-anchor="" x="19.17394731804" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >system_call_fastpath</text>
</g>
<g class="func_g" onmouseover="s('__run_hrtimer (172 samples, 0.04%)')" onmouseout="c()">
<title>__run_hrtimer (172 samples, 0.04%)</title><rect x="1126.6" y="369" width="0.4" height="15.0" fill="rgb(208,100,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_put (1,513 samples, 0.34%)')" onmouseout="c()">
<title>kvm_arch_vcpu_put (1,513 samples, 0.34%)</title><rect x="684.4" y="305" width="4.1" height="15.0" fill="rgb(243,89,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (138 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock_irqsave (138 samples, 0.03%)</title><rect x="152.6" y="225" width="0.4" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_system_time (2,186 samples, 0.49%)')" onmouseout="c()">
<title>account_system_time (2,186 samples, 0.49%)</title><rect x="158.2" y="241" width="5.9" height="15.0" fill="rgb(248,96,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (551 samples, 0.12%)')" onmouseout="c()">
<title>clockevents_program_event (551 samples, 0.12%)</title><rect x="1159.1" y="321" width="1.5" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic (378 samples, 0.09%)')" onmouseout="c()">
<title>copy_user_generic (378 samples, 0.09%)</title><rect x="568.8" y="321" width="1.0" height="15.0" fill="rgb(230,132,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_entity (5,411 samples, 1.22%)')" onmouseout="c()">
<title>enqueue_entity (5,411 samples, 1.22%)</title><rect x="1004.7" y="209" width="14.4" height="15.0" fill="rgb(214,151,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (126 samples, 0.03%)')" onmouseout="c()">
<title>copy_user_generic_string (126 samples, 0.03%)</title><rect x="569.8" y="321" width="0.3" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (16,604 samples, 3.74%)')" onmouseout="c()">
<title>__do_softirq (16,604 samples, 3.74%)</title><rect x="357.6" y="273" width="44.1" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
<text text-anchor="" x="360.550975821255" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__do..</text>
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (51 samples, 0.01%)')" onmouseout="c()">
<title>run_rebalance_domains (51 samples, 0.01%)</title><rect x="1044.0" y="321" width="0.1" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (43 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (43 samples, 0.01%)</title><rect x="1002.5" y="257" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_inject_irq (779 samples, 0.18%)')" onmouseout="c()">
<title>vmx_inject_irq (779 samples, 0.18%)</title><rect x="935.0" y="353" width="2.1" height="15.0" fill="rgb(206,62,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (176 samples, 0.04%)')" onmouseout="c()">
<title>native_read_msr_safe (176 samples, 0.04%)</title><rect x="687.0" y="257" width="0.4" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_event_task_tick (149 samples, 0.03%)')" onmouseout="c()">
<title>perf_event_task_tick (149 samples, 0.03%)</title><rect x="167.2" y="257" width="0.4" height="15.0" fill="rgb(224,223,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_disable (2,274 samples, 0.51%)')" onmouseout="c()">
<title>x86_pmu_disable (2,274 samples, 0.51%)</title><rect x="204.5" y="193" width="6.1" height="15.0" fill="rgb(223,177,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_mm_fault (1,904 samples, 0.43%)')" onmouseout="c()">
<title>handle_mm_fault (1,904 samples, 0.43%)</title><rect x="758.1" y="193" width="5.1" height="15.0" fill="rgb(0,232,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('reschedule_interrupt (802 samples, 0.18%)')" onmouseout="c()">
<title>reschedule_interrupt (802 samples, 0.18%)</title><rect x="1106.6" y="417" width="2.2" height="15.0" fill="rgb(213,96,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_call_function_single_interrupt (139 samples, 0.03%)')" onmouseout="c()">
<title>smp_call_function_single_interrupt (139 samples, 0.03%)</title><rect x="1046.4" y="401" width="0.4" height="15.0" fill="rgb(216,30,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn (38 samples, 0.01%)')" onmouseout="c()">
<title>unalias_gfn (38 samples, 0.01%)</title><rect x="757.1" y="241" width="0.1" height="15.0" fill="rgb(221,189,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (94 samples, 0.02%)')" onmouseout="c()">
<title>apic_update_ppr (94 samples, 0.02%)</title><rect x="568.1" y="321" width="0.2" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('insert_work (90 samples, 0.02%)')" onmouseout="c()">
<title>insert_work (90 samples, 0.02%)</title><rect x="400.8" y="209" width="0.3" height="15.0" fill="rgb(248,180,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (16,274 samples, 3.67%)')" onmouseout="c()">
<title>hrtimer_interrupt (16,274 samples, 3.67%)</title><rect x="993.1" y="385" width="43.2" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
<text text-anchor="" x="996.095464936879" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >hrti..</text>
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (260 samples, 0.06%)')" onmouseout="c()">
<title>__local_bh_enable (260 samples, 0.06%)</title><rect x="364.6" y="257" width="0.7" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (52 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (52 samples, 0.01%)</title><rect x="688.9" y="305" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (294 samples, 0.07%)')" onmouseout="c()">
<title>lapic_is_periodic (294 samples, 0.07%)</title><rect x="1029.8" y="353" width="0.8" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (103 samples, 0.02%)')" onmouseout="c()">
<title>update_curr (103 samples, 0.02%)</title><rect x="1020.4" y="209" width="0.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_disable_all (559 samples, 0.13%)')" onmouseout="c()">
<title>intel_pmu_disable_all (559 samples, 0.13%)</title><rect x="203.0" y="193" width="1.5" height="15.0" fill="rgb(251,19,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (101 samples, 0.02%)')" onmouseout="c()">
<title>account_entity_enqueue (101 samples, 0.02%)</title><rect x="631.1" y="241" width="0.3" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_forward (92 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_forward (92 samples, 0.02%)</title><rect x="126.4" y="289" width="0.3" height="15.0" fill="rgb(212,164,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_queues (314 samples, 0.07%)')" onmouseout="c()">
<title>hrtimer_run_queues (314 samples, 0.07%)</title><rect x="175.9" y="241" width="0.8" height="15.0" fill="rgb(250,176,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_pread64 (70 samples, 0.02%)')" onmouseout="c()">
<title>sys_pread64 (70 samples, 0.02%)</title><rect x="11.0" y="385" width="0.2" height="15.0" fill="rgb(0,198,59)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (148 samples, 0.03%)')" onmouseout="c()">
<title>irq_enter (148 samples, 0.03%)</title><rect x="1176.3" y="337" width="0.4" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lru_cache_add_lru (105 samples, 0.02%)')" onmouseout="c()">
<title>lru_cache_add_lru (105 samples, 0.02%)</title><rect x="762.8" y="145" width="0.3" height="15.0" fill="rgb(0,199,208)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_sched_qs (655 samples, 0.15%)')" onmouseout="c()">
<title>rcu_sched_qs (655 samples, 0.15%)</title><rect x="909.6" y="353" width="1.8" height="15.0" fill="rgb(239,116,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (45 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (45 samples, 0.01%)</title><rect x="820.3" y="209" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (197 samples, 0.04%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (197 samples, 0.04%)</title><rect x="692.9" y="305" width="0.5" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (83 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (83 samples, 0.02%)</title><rect x="150.7" y="257" width="0.2" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_write_guest (80 samples, 0.02%)')" onmouseout="c()">
<title>kvm_write_guest (80 samples, 0.02%)</title><rect x="909.4" y="353" width="0.2" height="15.0" fill="rgb(222,39,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_run_pending (263 samples, 0.06%)')" onmouseout="c()">
<title>hrtimer_run_pending (263 samples, 0.06%)</title><rect x="366.5" y="257" width="0.7" height="15.0" fill="rgb(230,45,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_tick (156 samples, 0.04%)')" onmouseout="c()">
<title>printk_tick (156 samples, 0.04%)</title><rect x="144.2" y="273" width="0.4" height="15.0" fill="rgb(211,104,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (102 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (102 samples, 0.02%)</title><rect x="1160.6" y="321" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_enter (126 samples, 0.03%)')" onmouseout="c()">
<title>irq_enter (126 samples, 0.03%)</title><rect x="34.4" y="337" width="0.3" height="15.0" fill="rgb(247,79,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_pending (401 samples, 0.09%)')" onmouseout="c()">
<title>__rcu_pending (401 samples, 0.09%)</title><rect x="154.7" y="257" width="1.0" height="15.0" fill="rgb(252,143,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_disable_all (1,320 samples, 0.30%)')" onmouseout="c()">
<title>intel_pmu_disable_all (1,320 samples, 0.30%)</title><rect x="206.0" y="177" width="3.5" height="15.0" fill="rgb(251,19,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (376 samples, 0.08%)')" onmouseout="c()">
<title>_spin_lock (376 samples, 0.08%)</title><rect x="115.2" y="289" width="1.0" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (595 samples, 0.13%)')" onmouseout="c()">
<title>_spin_lock_irqsave (595 samples, 0.13%)</title><rect x="790.3" y="193" width="1.6" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (62 samples, 0.01%)')" onmouseout="c()">
<title>idle_cpu (62 samples, 0.01%)</title><rect x="174.3" y="241" width="0.1" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_to_vapic (6,121 samples, 1.38%)')" onmouseout="c()">
<title>kvm_lapic_sync_to_vapic (6,121 samples, 1.38%)</title><rect x="574.9" y="337" width="16.3" height="15.0" fill="rgb(227,46,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (193 samples, 0.04%)')" onmouseout="c()">
<title>[unknown] (193 samples, 0.04%)</title><rect x="10.8" y="449" width="0.5" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mapping_level (2,723 samples, 0.61%)')" onmouseout="c()">
<title>mapping_level (2,723 samples, 0.61%)</title><rect x="756.6" y="273" width="7.2" height="15.0" fill="rgb(0,205,105)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_rt (41 samples, 0.01%)')" onmouseout="c()">
<title>pick_next_task_rt (41 samples, 0.01%)</title><rect x="612.5" y="321" width="0.1" height="15.0" fill="rgb(244,112,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (1,471 samples, 0.33%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (1,471 samples, 0.33%)</title><rect x="788.0" y="225" width="3.9" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (1,839 samples, 0.41%)')" onmouseout="c()">
<title>update_curr (1,839 samples, 0.41%)</title><rect x="642.5" y="241" width="4.9" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll_work (80 samples, 0.02%)')" onmouseout="c()">
<title>bnx2_poll_work (80 samples, 0.02%)</title><rect x="914.7" y="225" width="0.2" height="15.0" fill="rgb(253,180,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_tick (72 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock_tick (72 samples, 0.02%)</title><rect x="1041.5" y="321" width="0.2" height="15.0" fill="rgb(221,107,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (574 samples, 0.13%)')" onmouseout="c()">
<title>sched_clock (574 samples, 0.13%)</title><rect x="286.5" y="209" width="1.5" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('futex_wait_queue_me (50 samples, 0.01%)')" onmouseout="c()">
<title>futex_wait_queue_me (50 samples, 0.01%)</title><rect x="983.7" y="385" width="0.1" height="15.0" fill="rgb(210,34,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('printk_needs_cpu (78 samples, 0.02%)')" onmouseout="c()">
<title>printk_needs_cpu (78 samples, 0.02%)</title><rect x="1112.7" y="433" width="0.2" height="15.0" fill="rgb(225,165,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (44 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (44 samples, 0.01%)</title><rect x="971.2" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_idle (513 samples, 0.12%)')" onmouseout="c()">
<title>tick_nohz_stop_idle (513 samples, 0.12%)</title><rect x="1041.4" y="353" width="1.3" height="15.0" fill="rgb(210,186,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('set_next_entity (1,632 samples, 0.37%)')" onmouseout="c()">
<title>set_next_entity (1,632 samples, 0.37%)</title><rect x="1119.9" y="401" width="4.4" height="15.0" fill="rgb(232,112,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__apic_accept_irq (2,567 samples, 0.58%)')" onmouseout="c()">
<title>__apic_accept_irq (2,567 samples, 0.58%)</title><rect x="545.3" y="289" width="6.8" height="15.0" fill="rgb(223,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_interrupt_shadow (230 samples, 0.05%)')" onmouseout="c()">
<title>vmx_set_interrupt_shadow (230 samples, 0.05%)</title><rect x="772.5" y="305" width="0.6" height="15.0" fill="rgb(218,142,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rest_init (6,443 samples, 1.45%)')" onmouseout="c()">
<title>rest_init (6,443 samples, 1.45%)</title><rect x="1172.9" y="417" width="17.1" height="15.0" fill="rgb(232,7,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (7,415 samples, 1.67%)')" onmouseout="c()">
<title>activate_task (7,415 samples, 1.67%)</title><rect x="1002.7" y="257" width="19.7" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_read (397 samples, 0.09%)')" onmouseout="c()">
<title>sys_read (397 samples, 0.09%)</title><rect x="972.2" y="433" width="1.1" height="15.0" fill="rgb(223,64,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (316 samples, 0.07%)')" onmouseout="c()">
<title>schedule (316 samples, 0.07%)</title><rect x="1185.7" y="385" width="0.8" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_gp_end (236 samples, 0.05%)')" onmouseout="c()">
<title>rcu_process_gp_end (236 samples, 0.05%)</title><rect x="390.9" y="241" width="0.6" height="15.0" fill="rgb(245,96,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (43 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (43 samples, 0.01%)</title><rect x="1163.5" y="385" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (91 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_shares (91 samples, 0.02%)</title><rect x="1020.1" y="209" width="0.3" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (1,813 samples, 0.41%)')" onmouseout="c()">
<title>rb_insert_color (1,813 samples, 0.41%)</title><rect x="121.6" y="273" width="4.8" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_locked_key (74 samples, 0.02%)')" onmouseout="c()">
<title>__wake_up_locked_key (74 samples, 0.02%)</title><rect x="766.7" y="241" width="0.2" height="15.0" fill="rgb(0,214,187)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (247 samples, 0.06%)')" onmouseout="c()">
<title>native_read_tsc (247 samples, 0.06%)</title><rect x="341.3" y="241" width="0.7" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (650 samples, 0.15%)')" onmouseout="c()">
<title>rebalance_domains (650 samples, 0.15%)</title><rect x="1106.9" y="289" width="1.7" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_ts_time_stats (42 samples, 0.01%)')" onmouseout="c()">
<title>update_ts_time_stats (42 samples, 0.01%)</title><rect x="1170.7" y="433" width="0.1" height="15.0" fill="rgb(229,229,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (1,702 samples, 0.38%)')" onmouseout="c()">
<title>sched_clock_cpu (1,702 samples, 0.38%)</title><rect x="283.5" y="225" width="4.5" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (10,145 samples, 2.28%)')" onmouseout="c()">
<title>try_to_wake_up (10,145 samples, 2.28%)</title><rect x="1001.1" y="273" width="27.0" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
<text text-anchor="" x="1004.1404664572" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('kvm_write_guest (1,404 samples, 0.32%)')" onmouseout="c()">
<title>kvm_write_guest (1,404 samples, 0.32%)</title><rect x="690.0" y="337" width="3.7" height="15.0" fill="rgb(222,39,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (800 samples, 0.18%)')" onmouseout="c()">
<title>update_cfs_load (800 samples, 0.18%)</title><rect x="227.3" y="225" width="2.1" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (513 samples, 0.12%)')" onmouseout="c()">
<title>apic_timer_interrupt (513 samples, 0.12%)</title><rect x="1126.4" y="417" width="1.4" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (264 samples, 0.06%)')" onmouseout="c()">
<title>sched_clock (264 samples, 0.06%)</title><rect x="679.8" y="257" width="0.7" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_local_deliver (3,314 samples, 0.75%)')" onmouseout="c()">
<title>kvm_apic_local_deliver (3,314 samples, 0.75%)</title><rect x="544.9" y="305" width="8.8" height="15.0" fill="rgb(221,119,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_task_fair (135 samples, 0.03%)')" onmouseout="c()">
<title>dequeue_task_fair (135 samples, 0.03%)</title><rect x="649.9" y="289" width="0.4" height="15.0" fill="rgb(205,43,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timeval (52 samples, 0.01%)')" onmouseout="c()">
<title>ns_to_timeval (52 samples, 0.01%)</title><rect x="1100.6" y="401" width="0.1" height="15.0" fill="rgb(249,153,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_of (203 samples, 0.05%)')" onmouseout="c()">
<title>task_of (203 samples, 0.05%)</title><rect x="226.8" y="225" width="0.5" height="15.0" fill="rgb(226,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_vprintf (110 samples, 0.02%)')" onmouseout="c()">
<title>seq_vprintf (110 samples, 0.02%)</title><rect x="973.0" y="321" width="0.2" height="15.0" fill="rgb(210,166,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_unlock (111 samples, 0.03%)')" onmouseout="c()">
<title>mutex_unlock (111 samples, 0.03%)</title><rect x="611.7" y="321" width="0.3" height="15.0" fill="rgb(235,145,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_force_reprogram (55 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_force_reprogram (55 samples, 0.01%)</title><rect x="1186.9" y="321" width="0.2" height="15.0" fill="rgb(237,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_tick (39,576 samples, 8.91%)')" onmouseout="c()">
<title>scheduler_tick (39,576 samples, 8.91%)</title><rect x="182.8" y="257" width="105.2" height="15.0" fill="rgb(240,30,11)" rx="2" ry="2" />
<text text-anchor="" x="185.835974188317" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >scheduler_tick</text>
</g>
<g class="func_g" onmouseover="s('update_cfs_load (90 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_load (90 samples, 0.02%)</title><rect x="1019.9" y="209" width="0.2" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('zap_page_range (43 samples, 0.01%)')" onmouseout="c()">
<title>zap_page_range (43 samples, 0.01%)</title><rect x="980.6" y="417" width="0.1" height="15.0" fill="rgb(220,173,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_task_fair (95 samples, 0.02%)')" onmouseout="c()">
<title>enqueue_task_fair (95 samples, 0.02%)</title><rect x="1022.1" y="241" width="0.2" height="15.0" fill="rgb(251,134,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (160 samples, 0.04%)')" onmouseout="c()">
<title>update_cfs_load (160 samples, 0.04%)</title><rect x="276.0" y="241" width="0.4" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (2,595 samples, 0.58%)')" onmouseout="c()">
<title>update_rq_clock (2,595 samples, 0.58%)</title><rect x="281.1" y="241" width="6.9" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll_msix (44 samples, 0.01%)')" onmouseout="c()">
<title>bnx2_poll_msix (44 samples, 0.01%)</title><rect x="914.9" y="241" width="0.1" height="15.0" fill="rgb(241,170,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (1,019 samples, 0.23%)')" onmouseout="c()">
<title>vmcs_writel (1,019 samples, 0.23%)</title><rect x="889.1" y="321" width="2.7" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('srcu_read_lock (3,468 samples, 0.78%)')" onmouseout="c()">
<title>srcu_read_lock (3,468 samples, 0.78%)</title><rect x="709.3" y="337" width="9.2" height="15.0" fill="rgb(236,159,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_cancel (300 samples, 0.07%)')" onmouseout="c()">
<title>hrtimer_cancel (300 samples, 0.07%)</title><rect x="821.2" y="257" width="0.8" height="15.0" fill="rgb(211,94,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (169 samples, 0.04%)')" onmouseout="c()">
<title>call_softirq (169 samples, 0.04%)</title><rect x="1044.0" y="353" width="0.4" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (56 samples, 0.01%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (56 samples, 0.01%)</title><rect x="590.9" y="305" width="0.1" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_idle_sleep_event (63 samples, 0.01%)')" onmouseout="c()">
<title>sched_clock_idle_sleep_event (63 samples, 0.01%)</title><rect x="1113.7" y="433" width="0.2" height="15.0" fill="rgb(247,203,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__dequeue_entity (48 samples, 0.01%)')" onmouseout="c()">
<title>__dequeue_entity (48 samples, 0.01%)</title><rect x="1186.2" y="337" width="0.2" height="15.0" fill="rgb(254,73,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (498 samples, 0.11%)')" onmouseout="c()">
<title>kvm_timer_fn (498 samples, 0.11%)</title><rect x="299.1" y="305" width="1.4" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('atomic_notifier_call_chain (133 samples, 0.03%)')" onmouseout="c()">
<title>atomic_notifier_call_chain (133 samples, 0.03%)</title><rect x="1109.9" y="417" width="0.3" height="15.0" fill="rgb(249,80,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (169 samples, 0.04%)')" onmouseout="c()">
<title>do_softirq (169 samples, 0.04%)</title><rect x="1044.0" y="369" width="0.4" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (131 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (131 samples, 0.03%)</title><rect x="615.6" y="305" width="0.3" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_msr_safe (41 samples, 0.01%)')" onmouseout="c()">
<title>native_read_msr_safe (41 samples, 0.01%)</title><rect x="688.2" y="273" width="0.1" height="15.0" fill="rgb(238,178,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_save_host_state (2,528 samples, 0.57%)')" onmouseout="c()">
<title>vmx_save_host_state (2,528 samples, 0.57%)</title><rect x="833.2" y="337" width="6.7" height="15.0" fill="rgb(229,161,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (1,040 samples, 0.23%)')" onmouseout="c()">
<title>kvm_timer_fn (1,040 samples, 0.23%)</title><rect x="127.5" y="289" width="2.8" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (533 samples, 0.12%)')" onmouseout="c()">
<title>update_rq_clock (533 samples, 0.12%)</title><rect x="1020.7" y="225" width="1.4" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_pause (288 samples, 0.06%)')" onmouseout="c()">
<title>handle_pause (288 samples, 0.06%)</title><rect x="766.9" y="321" width="0.8" height="15.0" fill="rgb(223,162,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_user_pages_fast (76 samples, 0.02%)')" onmouseout="c()">
<title>get_user_pages_fast (76 samples, 0.02%)</title><rect x="756.3" y="241" width="0.2" height="15.0" fill="rgb(0,196,67)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (70 samples, 0.02%)')" onmouseout="c()">
<title>apic_update_ppr (70 samples, 0.02%)</title><rect x="514.2" y="289" width="0.2" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (40 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_interrupt (40 samples, 0.01%)</title><rect x="626.0" y="273" width="0.1" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('futex_wake (54 samples, 0.01%)')" onmouseout="c()">
<title>futex_wake (54 samples, 0.01%)</title><rect x="983.5" y="401" width="0.1" height="15.0" fill="rgb(243,120,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unalias_gfn_instantiation (202 samples, 0.05%)')" onmouseout="c()">
<title>unalias_gfn_instantiation (202 samples, 0.05%)</title><rect x="596.0" y="289" width="0.6" height="15.0" fill="rgb(212,78,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_task_sched_out (172 samples, 0.04%)')" onmouseout="c()">
<title>__perf_event_task_sched_out (172 samples, 0.04%)</title><rect x="618.7" y="289" width="0.4" height="15.0" fill="rgb(233,222,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (72 samples, 0.02%)')" onmouseout="c()">
<title>read_tsc (72 samples, 0.02%)</title><rect x="148.1" y="273" width="0.2" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (564 samples, 0.13%)')" onmouseout="c()">
<title>enqueue_hrtimer (564 samples, 0.13%)</title><rect x="1163.6" y="385" width="1.5" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_work_run (218 samples, 0.05%)')" onmouseout="c()">
<title>irq_work_run (218 samples, 0.05%)</title><rect x="141.9" y="273" width="0.6" height="15.0" fill="rgb(252,101,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_ioapic_handles_vector (339 samples, 0.08%)')" onmouseout="c()">
<title>kvm_ioapic_handles_vector (339 samples, 0.08%)</title><rect x="570.1" y="321" width="0.9" height="15.0" fill="rgb(233,138,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (186 samples, 0.04%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (186 samples, 0.04%)</title><rect x="1187.4" y="353" width="0.5" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (196 samples, 0.04%)')" onmouseout="c()">
<title>ktime_get (196 samples, 0.04%)</title><rect x="1167.9" y="417" width="0.6" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_enabled (337 samples, 0.08%)')" onmouseout="c()">
<title>kvm_lapic_enabled (337 samples, 0.08%)</title><rect x="555.8" y="337" width="0.9" height="15.0" fill="rgb(211,200,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (86 samples, 0.02%)')" onmouseout="c()">
<title>update_cfs_load (86 samples, 0.02%)</title><rect x="1108.2" y="257" width="0.2" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('timekeeping_update (46 samples, 0.01%)')" onmouseout="c()">
<title>timekeeping_update (46 samples, 0.01%)</title><rect x="1041.2" y="321" width="0.1" height="15.0" fill="rgb(253,156,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unmap_region (52 samples, 0.01%)')" onmouseout="c()">
<title>unmap_region (52 samples, 0.01%)</title><rect x="973.4" y="401" width="0.1" height="15.0" fill="rgb(241,81,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (1,376 samples, 0.31%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (1,376 samples, 0.31%)</title><rect x="150.3" y="273" width="3.6" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (101 samples, 0.02%)')" onmouseout="c()">
<title>hrtimer_interrupt (101 samples, 0.02%)</title><rect x="989.7" y="401" width="0.3" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pwritev64 (41 samples, 0.01%)')" onmouseout="c()">
<title>pwritev64 (41 samples, 0.01%)</title><rect x="982.9" y="465" width="0.1" height="15.0" fill="rgb(0,206,169)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (185 samples, 0.04%)')" onmouseout="c()">
<title>pick_next_task_fair (185 samples, 0.04%)</title><rect x="1186.0" y="369" width="0.5" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('napi_skb_finish (53 samples, 0.01%)')" onmouseout="c()">
<title>napi_skb_finish (53 samples, 0.01%)</title><rect x="914.8" y="193" width="0.1" height="15.0" fill="rgb(250,77,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (113 samples, 0.03%)')" onmouseout="c()">
<title>native_apic_mem_write (113 samples, 0.03%)</title><rect x="1035.5" y="321" width="0.3" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_next_timer_interrupt (53 samples, 0.01%)')" onmouseout="c()">
<title>get_next_timer_interrupt (53 samples, 0.01%)</title><rect x="1188.4" y="369" width="0.2" height="15.0" fill="rgb(245,174,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty_in_slot (63 samples, 0.01%)')" onmouseout="c()">
<title>mark_page_dirty_in_slot (63 samples, 0.01%)</title><rect x="574.8" y="321" width="0.1" height="15.0" fill="rgb(209,110,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_page_from_freelist (334 samples, 0.08%)')" onmouseout="c()">
<title>get_page_from_freelist (334 samples, 0.08%)</title><rect x="761.0" y="129" width="0.9" height="15.0" fill="rgb(0,232,56)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (55 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (55 samples, 0.01%)</title><rect x="983.5" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tls (168 samples, 0.04%)')" onmouseout="c()">
<title>native_load_tls (168 samples, 0.04%)</title><rect x="981.3" y="465" width="0.4" height="15.0" fill="rgb(235,161,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (55 samples, 0.01%)')" onmouseout="c()">
<title>rb_erase (55 samples, 0.01%)</title><rect x="1122.6" y="385" width="0.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (6,804 samples, 1.53%)')" onmouseout="c()">
<title>find_busiest_group (6,804 samples, 1.53%)</title><rect x="650.8" y="305" width="18.1" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (647 samples, 0.15%)')" onmouseout="c()">
<title>ktime_get (647 samples, 0.15%)</title><rect x="142.5" y="273" width="1.7" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_sched_qs (76 samples, 0.02%)')" onmouseout="c()">
<title>rcu_sched_qs (76 samples, 0.02%)</title><rect x="1113.5" y="433" width="0.2" height="15.0" fill="rgb(239,116,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (246 samples, 0.06%)')" onmouseout="c()">
<title>native_read_tsc (246 samples, 0.06%)</title><rect x="298.5" y="273" width="0.6" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (249 samples, 0.06%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (249 samples, 0.06%)</title><rect x="799.8" y="225" width="0.6" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (103 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get (103 samples, 0.02%)</title><rect x="1039.4" y="353" width="0.2" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (48 samples, 0.01%)')" onmouseout="c()">
<title>rebalance_domains (48 samples, 0.01%)</title><rect x="1044.0" y="305" width="0.1" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (131 samples, 0.03%)')" onmouseout="c()">
<title>native_sched_clock (131 samples, 0.03%)</title><rect x="1021.4" y="193" width="0.4" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_process_callbacks (323 samples, 0.07%)')" onmouseout="c()">
<title>__rcu_process_callbacks (323 samples, 0.07%)</title><rect x="365.3" y="257" width="0.9" height="15.0" fill="rgb(211,185,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__lru_cache_add (98 samples, 0.02%)')" onmouseout="c()">
<title>__lru_cache_add (98 samples, 0.02%)</title><rect x="762.8" y="129" width="0.3" height="15.0" fill="rgb(0,213,90)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('autoremove_wake_function (629 samples, 0.14%)')" onmouseout="c()">
<title>autoremove_wake_function (629 samples, 0.14%)</title><rect x="1174.1" y="257" width="1.6" height="15.0" fill="rgb(226,61,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kthread (1,174 samples, 0.26%)')" onmouseout="c()">
<title>kthread (1,174 samples, 0.26%)</title><rect x="977.4" y="449" width="3.1" height="15.0" fill="rgb(254,163,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_timer (55 samples, 0.01%)')" onmouseout="c()">
<title>do_timer (55 samples, 0.01%)</title><rect x="1150.2" y="401" width="0.1" height="15.0" fill="rgb(223,101,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_run (19,545 samples, 4.40%)')" onmouseout="c()">
<title>vmx_vcpu_run (19,545 samples, 4.40%)</title><rect x="839.9" y="337" width="51.9" height="15.0" fill="rgb(235,49,53)" rx="2" ry="2" />
<text text-anchor="" x="842.892271135286" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vmx_v..</text>
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (469 samples, 0.11%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (469 samples, 0.11%)</title><rect x="501.4" y="337" width="1.3" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__blockdev_direct_IO (62 samples, 0.01%)')" onmouseout="c()">
<title>__blockdev_direct_IO (62 samples, 0.01%)</title><rect x="11.0" y="289" width="0.2" height="15.0" fill="rgb(0,239,138)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (582 samples, 0.13%)')" onmouseout="c()">
<title>rcu_irq_exit (582 samples, 0.13%)</title><rect x="406.7" y="305" width="1.5" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_irq_delivery_to_apic (1,284 samples, 0.29%)')" onmouseout="c()">
<title>kvm_irq_delivery_to_apic (1,284 samples, 0.29%)</title><rect x="795.4" y="241" width="3.4" height="15.0" fill="rgb(213,217,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (42 samples, 0.01%)')" onmouseout="c()">
<title>__rb_rotate_right (42 samples, 0.01%)</title><rect x="1164.1" y="369" width="0.1" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('alloc_pages_vma (1,316 samples, 0.30%)')" onmouseout="c()">
<title>alloc_pages_vma (1,316 samples, 0.30%)</title><rect x="758.6" y="161" width="3.5" height="15.0" fill="rgb(0,200,122)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (1,633 samples, 0.37%)')" onmouseout="c()">
<title>copy_user_generic_string (1,633 samples, 0.37%)</title><rect x="582.6" y="321" width="4.3" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_interrupt (42 samples, 0.01%)')" onmouseout="c()">
<title>hrtimer_interrupt (42 samples, 0.01%)</title><rect x="497.4" y="305" width="0.2" height="15.0" fill="rgb(252,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_cached (613 samples, 0.14%)')" onmouseout="c()">
<title>kvm_read_guest_cached (613 samples, 0.14%)</title><rect x="597.1" y="337" width="1.6" height="15.0" fill="rgb(224,155,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_busiest_group (54 samples, 0.01%)')" onmouseout="c()">
<title>find_busiest_group (54 samples, 0.01%)</title><rect x="1127.4" y="289" width="0.1" height="15.0" fill="rgb(221,103,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('proc_tgid_stat (155 samples, 0.03%)')" onmouseout="c()">
<title>proc_tgid_stat (155 samples, 0.03%)</title><rect x="972.8" y="369" width="0.4" height="15.0" fill="rgb(236,217,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_IRQ (81 samples, 0.02%)')" onmouseout="c()">
<title>do_IRQ (81 samples, 0.02%)</title><rect x="1185.1" y="353" width="0.2" height="15.0" fill="rgb(219,133,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (227 samples, 0.05%)')" onmouseout="c()">
<title>do_softirq (227 samples, 0.05%)</title><rect x="1184.5" y="305" width="0.6" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('dequeue_entity (6,942 samples, 1.56%)')" onmouseout="c()">
<title>dequeue_entity (6,942 samples, 1.56%)</title><rect x="629.0" y="257" width="18.4" height="15.0" fill="rgb(208,108,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (685 samples, 0.15%)')" onmouseout="c()">
<title>update_cfs_load (685 samples, 0.15%)</title><rect x="676.1" y="289" width="1.8" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_load (211 samples, 0.05%)')" onmouseout="c()">
<title>kvm_arch_vcpu_load (211 samples, 0.05%)</title><rect x="603.8" y="321" width="0.5" height="15.0" fill="rgb(215,87,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (644 samples, 0.15%)')" onmouseout="c()">
<title>__wake_up_common (644 samples, 0.15%)</title><rect x="1174.0" y="273" width="1.7" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_vfs_ioctl (348,207 samples, 78.43%)')" onmouseout="c()">
<title>do_vfs_ioctl (348,207 samples, 78.43%)</title><rect x="16.2" y="401" width="925.4" height="15.0" fill="rgb(240,120,33)" rx="2" ry="2" />
<text text-anchor="" x="19.17394731804" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_vfs_ioctl</text>
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (531 samples, 0.12%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (531 samples, 0.12%)</title><rect x="1040.0" y="353" width="1.4" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_sched_tick (64 samples, 0.01%)')" onmouseout="c()">
<title>tick_nohz_stop_sched_tick (64 samples, 0.01%)</title><rect x="1171.8" y="449" width="0.1" height="15.0" fill="rgb(217,57,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('group_sched_in (95 samples, 0.02%)')" onmouseout="c()">
<title>group_sched_in (95 samples, 0.02%)</title><rect x="1046.5" y="337" width="0.2" height="15.0" fill="rgb(254,64,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tdp_page_fault (3,636 samples, 0.82%)')" onmouseout="c()">
<title>tdp_page_fault (3,636 samples, 0.82%)</title><rect x="754.4" y="289" width="9.7" height="15.0" fill="rgb(0,214,90)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cpu_load (291 samples, 0.07%)')" onmouseout="c()">
<title>update_cpu_load (291 samples, 0.07%)</title><rect x="289.8" y="257" width="0.8" height="15.0" fill="rgb(210,180,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_ind_direct_IO (62 samples, 0.01%)')" onmouseout="c()">
<title>ext4_ind_direct_IO (62 samples, 0.01%)</title><rect x="11.0" y="305" width="0.2" height="15.0" fill="rgb(0,202,96)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('netif_receive_skb (53 samples, 0.01%)')" onmouseout="c()">
<title>netif_receive_skb (53 samples, 0.01%)</title><rect x="914.8" y="177" width="0.1" height="15.0" fill="rgb(252,105,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('grab_cache_page_write_begin (46 samples, 0.01%)')" onmouseout="c()">
<title>grab_cache_page_write_begin (46 samples, 0.01%)</title><rect x="974.2" y="257" width="0.2" height="15.0" fill="rgb(213,2,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_exit (538 samples, 0.12%)')" onmouseout="c()">
<title>rcu_irq_exit (538 samples, 0.12%)</title><rect x="412.3" y="321" width="1.4" height="15.0" fill="rgb(253,130,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_idle_sibling (42 samples, 0.01%)')" onmouseout="c()">
<title>select_idle_sibling (42 samples, 0.01%)</title><rect x="1024.0" y="257" width="0.1" height="15.0" fill="rgb(233,205,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mutex_lock (231 samples, 0.05%)')" onmouseout="c()">
<title>mutex_lock (231 samples, 0.05%)</title><rect x="682.8" y="305" width="0.6" height="15.0" fill="rgb(238,5,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (167 samples, 0.04%)')" onmouseout="c()">
<title>[unknown] (167 samples, 0.04%)</title><rect x="1171.9" y="449" width="0.5" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('net_rx_action (40 samples, 0.01%)')" onmouseout="c()">
<title>net_rx_action (40 samples, 0.01%)</title><rect x="1185.2" y="273" width="0.1" height="15.0" fill="rgb(234,84,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (86 samples, 0.02%)')" onmouseout="c()">
<title>clockevents_program_event (86 samples, 0.02%)</title><rect x="1158.2" y="337" width="0.2" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_munmap (61 samples, 0.01%)')" onmouseout="c()">
<title>do_munmap (61 samples, 0.01%)</title><rect x="973.4" y="417" width="0.1" height="15.0" fill="rgb(212,226,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timespec (79 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timespec (79 samples, 0.02%)</title><rect x="1100.3" y="401" width="0.3" height="15.0" fill="rgb(227,143,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (81 samples, 0.02%)')" onmouseout="c()">
<title>default_wake_function (81 samples, 0.02%)</title><rect x="400.8" y="145" width="0.2" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('default_wake_function (618 samples, 0.14%)')" onmouseout="c()">
<title>default_wake_function (618 samples, 0.14%)</title><rect x="1174.1" y="241" width="1.6" height="15.0" fill="rgb(208,124,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_cache_reg (686 samples, 0.15%)')" onmouseout="c()">
<title>vmx_cache_reg (686 samples, 0.15%)</title><rect x="827.8" y="321" width="1.8" height="15.0" fill="rgb(209,28,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_read_guest_cached (1,072 samples, 0.24%)')" onmouseout="c()">
<title>kvm_read_guest_cached (1,072 samples, 0.24%)</title><rect x="571.0" y="321" width="2.9" height="15.0" fill="rgb(224,155,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (598 samples, 0.13%)')" onmouseout="c()">
<title>try_to_wake_up (598 samples, 0.13%)</title><rect x="1174.1" y="225" width="1.6" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (226 samples, 0.05%)')" onmouseout="c()">
<title>__do_softirq (226 samples, 0.05%)</title><rect x="1184.5" y="273" width="0.6" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (103 samples, 0.02%)')" onmouseout="c()">
<title>notifier_call_chain (103 samples, 0.02%)</title><rect x="1110.0" y="401" width="0.2" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (43 samples, 0.01%)')" onmouseout="c()">
<title>gfn_to_hva (43 samples, 0.01%)</title><rect x="756.1" y="257" width="0.1" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irqsave (124 samples, 0.03%)')" onmouseout="c()">
<title>_spin_lock_irqsave (124 samples, 0.03%)</title><rect x="603.2" y="305" width="0.3" height="15.0" fill="rgb(209,137,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rmqueue (53 samples, 0.01%)')" onmouseout="c()">
<title>__rmqueue (53 samples, 0.01%)</title><rect x="761.5" y="113" width="0.2" height="15.0" fill="rgb(0,238,201)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (112 samples, 0.03%)')" onmouseout="c()">
<title>update_cfs_shares (112 samples, 0.03%)</title><rect x="1174.7" y="145" width="0.3" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_kick (595 samples, 0.13%)')" onmouseout="c()">
<title>kvm_vcpu_kick (595 samples, 0.13%)</title><rect x="552.1" y="289" width="1.6" height="15.0" fill="rgb(217,38,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('remote_function (100 samples, 0.02%)')" onmouseout="c()">
<title>remote_function (100 samples, 0.02%)</title><rect x="1046.5" y="369" width="0.2" height="15.0" fill="rgb(252,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (60 samples, 0.01%)')" onmouseout="c()">
<title>lock_hrtimer_base (60 samples, 0.01%)</title><rect x="818.6" y="209" width="0.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (337 samples, 0.08%)')" onmouseout="c()">
<title>rb_erase (337 samples, 0.08%)</title><rect x="133.4" y="289" width="0.9" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (215 samples, 0.05%)')" onmouseout="c()">
<title>_spin_lock_irq (215 samples, 0.05%)</title><rect x="400.1" y="241" width="0.6" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_right (255 samples, 0.06%)')" onmouseout="c()">
<title>__rb_rotate_right (255 samples, 0.06%)</title><rect x="809.1" y="177" width="0.7" height="15.0" fill="rgb(206,117,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (554 samples, 0.12%)')" onmouseout="c()">
<title>update_cfs_load (554 samples, 0.12%)</title><rect x="1010.3" y="193" width="1.5" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('preempt_notifier_unregister (83 samples, 0.02%)')" onmouseout="c()">
<title>preempt_notifier_unregister (83 samples, 0.02%)</title><rect x="613.6" y="321" width="0.3" height="15.0" fill="rgb(249,30,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('get_next_timer_interrupt (65 samples, 0.01%)')" onmouseout="c()">
<title>get_next_timer_interrupt (65 samples, 0.01%)</title><rect x="1110.6" y="433" width="0.2" height="15.0" fill="rgb(245,174,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_timer_interrupt (66 samples, 0.01%)')" onmouseout="c()">
<title>apic_timer_interrupt (66 samples, 0.01%)</title><rect x="497.4" y="337" width="0.2" height="15.0" fill="rgb(239,200,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('x86_pmu_enable (94 samples, 0.02%)')" onmouseout="c()">
<title>x86_pmu_enable (94 samples, 0.02%)</title><rect x="1046.5" y="289" width="0.2" height="15.0" fill="rgb(232,155,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clear_buddies (60 samples, 0.01%)')" onmouseout="c()">
<title>clear_buddies (60 samples, 0.01%)</title><rect x="628.8" y="257" width="0.2" height="15.0" fill="rgb(216,188,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__perf_event_task_sched_out (48 samples, 0.01%)')" onmouseout="c()">
<title>__perf_event_task_sched_out (48 samples, 0.01%)</title><rect x="618.2" y="305" width="0.1" height="15.0" fill="rgb(233,222,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (133 samples, 0.03%)')" onmouseout="c()">
<title>account_entity_enqueue (133 samples, 0.03%)</title><rect x="1004.3" y="209" width="0.4" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_group_power (42 samples, 0.01%)')" onmouseout="c()">
<title>update_group_power (42 samples, 0.01%)</title><rect x="1107.6" y="257" width="0.1" height="15.0" fill="rgb(245,109,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hva_to_pfn (2,409 samples, 0.54%)')" onmouseout="c()">
<title>hva_to_pfn (2,409 samples, 0.54%)</title><rect x="757.2" y="257" width="6.4" height="15.0" fill="rgb(0,194,206)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vfs_ioctl (348,207 samples, 78.43%)')" onmouseout="c()">
<title>vfs_ioctl (348,207 samples, 78.43%)</title><rect x="16.2" y="385" width="925.4" height="15.0" fill="rgb(237,75,25)" rx="2" ry="2" />
<text text-anchor="" x="19.17394731804" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vfs_ioctl</text>
</g>
<g class="func_g" onmouseover="s('ktime_get (729 samples, 0.16%)')" onmouseout="c()">
<title>ktime_get (729 samples, 0.16%)</title><rect x="818.8" y="225" width="1.9" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timeval (80 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timeval (80 samples, 0.02%)</title><rect x="1105.9" y="417" width="0.3" height="15.0" fill="rgb(249,153,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_user_generic_string (486 samples, 0.11%)')" onmouseout="c()">
<title>copy_user_generic_string (486 samples, 0.11%)</title><rect x="593.9" y="321" width="1.3" height="15.0" fill="rgb(228,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (43 samples, 0.01%)')" onmouseout="c()">
<title>system_call_fastpath (43 samples, 0.01%)</title><rect x="980.6" y="449" width="0.1" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_read (292 samples, 0.07%)')" onmouseout="c()">
<title>seq_read (292 samples, 0.07%)</title><rect x="972.5" y="401" width="0.8" height="15.0" fill="rgb(237,158,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_idle (73 samples, 0.02%)')" onmouseout="c()">
<title>exit_idle (73 samples, 0.02%)</title><rect x="32.2" y="337" width="0.2" height="15.0" fill="rgb(217,85,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (76 samples, 0.02%)')" onmouseout="c()">
<title>sched_clock (76 samples, 0.02%)</title><rect x="1021.0" y="209" width="0.2" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (143,572 samples, 32.34%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (143,572 samples, 32.34%)</title><rect x="42.0" y="337" width="381.6" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
<text text-anchor="" x="44.9913060125905" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >smp_apic_timer_interrupt</text>
</g>
<g class="func_g" onmouseover="s('__vmx_load_host_state (92 samples, 0.02%)')" onmouseout="c()">
<title>__vmx_load_host_state (92 samples, 0.02%)</title><rect x="684.6" y="289" width="0.2" height="15.0" fill="rgb(238,211,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_local_bh_enable (62 samples, 0.01%)')" onmouseout="c()">
<title>_local_bh_enable (62 samples, 0.01%)</title><rect x="1037.0" y="369" width="0.1" height="15.0" fill="rgb(210,228,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (1,664 samples, 0.37%)')" onmouseout="c()">
<title>rb_insert_color (1,664 samples, 0.37%)</title><rect x="809.8" y="177" width="4.4" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_memslot (64 samples, 0.01%)')" onmouseout="c()">
<title>gfn_to_memslot (64 samples, 0.01%)</title><rect x="757.0" y="257" width="0.2" height="15.0" fill="rgb(239,135,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__alloc_pages_nodemask (1,209 samples, 0.27%)')" onmouseout="c()">
<title>__alloc_pages_nodemask (1,209 samples, 0.27%)</title><rect x="758.7" y="145" width="3.3" height="15.0" fill="rgb(0,190,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_accept_pic_intr (135 samples, 0.03%)')" onmouseout="c()">
<title>kvm_apic_accept_pic_intr (135 samples, 0.03%)</title><rect x="606.5" y="289" width="0.4" height="15.0" fill="rgb(249,176,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (11,691 samples, 2.63%)')" onmouseout="c()">
<title>kvm_timer_fn (11,691 samples, 2.63%)</title><rect x="998.7" y="353" width="31.1" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
<text text-anchor="" x="1001.71128529117" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kv..</text>
</g>
<g class="func_g" onmouseover="s('vmx_get_msr (2,169 samples, 0.49%)')" onmouseout="c()">
<title>vmx_get_msr (2,169 samples, 0.49%)</title><rect x="925.3" y="353" width="5.7" height="15.0" fill="rgb(217,85,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_handle_exit (31,968 samples, 7.20%)')" onmouseout="c()">
<title>vmx_handle_exit (31,968 samples, 7.20%)</title><rect x="745.4" y="337" width="85.0" height="15.0" fill="rgb(237,179,1)" rx="2" ry="2" />
<text text-anchor="" x="748.427953647083" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >vmx_handle..</text>
</g>
<g class="func_g" onmouseover="s('update_process_times (40 samples, 0.01%)')" onmouseout="c()">
<title>update_process_times (40 samples, 0.01%)</title><rect x="1030.9" y="337" width="0.1" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (62 samples, 0.01%)')" onmouseout="c()">
<title>rb_next (62 samples, 0.01%)</title><rect x="1167.4" y="385" width="0.1" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sem_wait (84 samples, 0.02%)')" onmouseout="c()">
<title>sem_wait (84 samples, 0.02%)</title><rect x="983.6" y="465" width="0.2" height="15.0" fill="rgb(233,162,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (102 samples, 0.02%)')" onmouseout="c()">
<title>ktime_get_update_offsets (102 samples, 0.02%)</title><rect x="1031.4" y="369" width="0.3" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (278 samples, 0.06%)')" onmouseout="c()">
<title>ktime_get_update_offsets (278 samples, 0.06%)</title><rect x="408.2" y="321" width="0.7" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (217 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_start (217 samples, 0.05%)</title><rect x="792.7" y="241" width="0.6" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (173 samples, 0.04%)')" onmouseout="c()">
<title>lock_hrtimer_base (173 samples, 0.04%)</title><rect x="1165.9" y="385" width="0.5" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (110 samples, 0.02%)')" onmouseout="c()">
<title>native_read_tsc (110 samples, 0.02%)</title><rect x="287.7" y="177" width="0.3" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('net_rx_action (158 samples, 0.04%)')" onmouseout="c()">
<title>net_rx_action (158 samples, 0.04%)</title><rect x="914.7" y="257" width="0.4" height="15.0" fill="rgb(234,84,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('wake_futex (47 samples, 0.01%)')" onmouseout="c()">
<title>wake_futex (47 samples, 0.01%)</title><rect x="983.5" y="385" width="0.1" height="15.0" fill="rgb(0,197,186)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_sched_qs (283 samples, 0.06%)')" onmouseout="c()">
<title>rcu_sched_qs (283 samples, 0.06%)</title><rect x="708.2" y="337" width="0.7" height="15.0" fill="rgb(239,116,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_load (681 samples, 0.15%)')" onmouseout="c()">
<title>kvm_arch_vcpu_load (681 samples, 0.15%)</title><rect x="681.0" y="305" width="1.8" height="15.0" fill="rgb(215,87,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (226 samples, 0.05%)')" onmouseout="c()">
<title>call_softirq (226 samples, 0.05%)</title><rect x="1184.5" y="289" width="0.6" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (138 samples, 0.03%)')" onmouseout="c()">
<title>update_cfs_shares (138 samples, 0.03%)</title><rect x="797.3" y="33" width="0.4" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (682 samples, 0.15%)')" onmouseout="c()">
<title>native_sched_clock (682 samples, 0.15%)</title><rect x="284.7" y="209" width="1.8" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('setup_vmcs_config (830 samples, 0.19%)')" onmouseout="c()">
<title>setup_vmcs_config (830 samples, 0.19%)</title><rect x="824.4" y="321" width="2.2" height="15.0" fill="rgb(236,211,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_sync_write (346 samples, 0.08%)')" onmouseout="c()">
<title>do_sync_write (346 samples, 0.08%)</title><rect x="974.0" y="353" width="0.9" height="15.0" fill="rgb(226,116,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_inject_irq (775 samples, 0.17%)')" onmouseout="c()">
<title>vmx_inject_irq (775 samples, 0.17%)</title><rect x="830.4" y="337" width="2.1" height="15.0" fill="rgb(206,62,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('free_block (57 samples, 0.01%)')" onmouseout="c()">
<title>free_block (57 samples, 0.01%)</title><rect x="979.9" y="385" width="0.1" height="15.0" fill="rgb(207,157,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('bnx2_poll_work (43 samples, 0.01%)')" onmouseout="c()">
<title>bnx2_poll_work (43 samples, 0.01%)</title><rect x="914.9" y="225" width="0.1" height="15.0" fill="rgb(253,180,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (83 samples, 0.02%)')" onmouseout="c()">
<title>clockevents_program_event (83 samples, 0.02%)</title><rect x="1145.8" y="353" width="0.3" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gup_pte_range (45 samples, 0.01%)')" onmouseout="c()">
<title>gup_pte_range (45 samples, 0.01%)</title><rect x="763.5" y="209" width="0.1" height="15.0" fill="rgb(0,228,134)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sys_getdents (58 samples, 0.01%)')" onmouseout="c()">
<title>sys_getdents (58 samples, 0.01%)</title><rect x="973.7" y="433" width="0.1" height="15.0" fill="rgb(233,30,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_path_lookup (44 samples, 0.01%)')" onmouseout="c()">
<title>do_path_lookup (44 samples, 0.01%)</title><rect x="971.9" y="385" width="0.1" height="15.0" fill="rgb(236,229,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_x2apic_msr_write (14,992 samples, 3.38%)')" onmouseout="c()">
<title>kvm_x2apic_msr_write (14,992 samples, 3.38%)</title><rect x="782.7" y="273" width="39.9" height="15.0" fill="rgb(227,74,43)" rx="2" ry="2" />
<text text-anchor="" x="785.745385542304" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm..</text>
</g>
<g class="func_g" onmouseover="s('ntp_tick_length (195 samples, 0.04%)')" onmouseout="c()">
<title>ntp_tick_length (195 samples, 0.04%)</title><rect x="152.6" y="241" width="0.5" height="15.0" fill="rgb(212,167,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('child_rip (107 samples, 0.02%)')" onmouseout="c()">
<title>child_rip (107 samples, 0.02%)</title><rect x="977.1" y="449" width="0.3" height="15.0" fill="rgb(211,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_cr0 (828 samples, 0.19%)')" onmouseout="c()">
<title>native_read_cr0 (828 samples, 0.19%)</title><rect x="696.5" y="337" width="2.2" height="15.0" fill="rgb(248,172,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('restore_args (269 samples, 0.06%)')" onmouseout="c()">
<title>restore_args (269 samples, 0.06%)</title><rect x="911.6" y="353" width="0.7" height="15.0" fill="rgb(243,81,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (6,480 samples, 1.46%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (6,480 samples, 1.46%)</title><rect x="514.4" y="337" width="17.2" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_get_cr8 (785 samples, 0.18%)')" onmouseout="c()">
<title>kvm_lapic_get_cr8 (785 samples, 0.18%)</title><rect x="558.7" y="337" width="2.1" height="15.0" fill="rgb(234,219,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('notifier_call_chain (69 samples, 0.02%)')" onmouseout="c()">
<title>notifier_call_chain (69 samples, 0.02%)</title><rect x="1110.3" y="417" width="0.2" height="15.0" fill="rgb(240,117,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (375 samples, 0.08%)')" onmouseout="c()">
<title>calc_delta_mine (375 samples, 0.08%)</title><rect x="225.8" y="225" width="1.0" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (350 samples, 0.08%)')" onmouseout="c()">
<title>native_apic_mem_write (350 samples, 0.08%)</title><rect x="1159.6" y="305" width="1.0" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (357 samples, 0.08%)')" onmouseout="c()">
<title>native_write_msr_safe (357 samples, 0.08%)</title><rect x="981.7" y="465" width="1.0" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_set_msr (298 samples, 0.07%)')" onmouseout="c()">
<title>vmx_set_msr (298 samples, 0.07%)</title><rect x="829.6" y="321" width="0.8" height="15.0" fill="rgb(249,17,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_restart_sched_tick (8,323 samples, 1.87%)')" onmouseout="c()">
<title>tick_nohz_restart_sched_tick (8,323 samples, 1.87%)</title><rect x="1128.4" y="433" width="22.1" height="15.0" fill="rgb(210,23,25)" rx="2" ry="2" />
<text text-anchor="" x="1131.35620572767" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('select_nohz_load_balancer (432 samples, 0.10%)')" onmouseout="c()">
<title>select_nohz_load_balancer (432 samples, 0.10%)</title><rect x="1149.0" y="417" width="1.1" height="15.0" fill="rgb(224,12,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (133 samples, 0.03%)')" onmouseout="c()">
<title>account_entity_enqueue (133 samples, 0.03%)</title><rect x="639.4" y="225" width="0.3" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_interrupt_allowed (518 samples, 0.12%)')" onmouseout="c()">
<title>vmx_interrupt_allowed (518 samples, 0.12%)</title><rect x="937.1" y="353" width="1.3" height="15.0" fill="rgb(206,40,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__timer_stats_hrtimer_set_start_info (123 samples, 0.03%)')" onmouseout="c()">
<title>__timer_stats_hrtimer_set_start_info (123 samples, 0.03%)</title><rect x="1163.2" y="385" width="0.3" height="15.0" fill="rgb(211,194,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (85 samples, 0.02%)')" onmouseout="c()">
<title>__wake_up_common (85 samples, 0.02%)</title><rect x="400.8" y="177" width="0.3" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (793 samples, 0.18%)')" onmouseout="c()">
<title>rb_next (793 samples, 0.18%)</title><rect x="135.1" y="289" width="2.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (366 samples, 0.08%)')" onmouseout="c()">
<title>ktime_get (366 samples, 0.08%)</title><rect x="794.3" y="241" width="1.0" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_irq_enter (145 samples, 0.03%)')" onmouseout="c()">
<title>rcu_irq_enter (145 samples, 0.03%)</title><rect x="1037.7" y="369" width="0.4" height="15.0" fill="rgb(233,147,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_sched_clock (60 samples, 0.01%)')" onmouseout="c()">
<title>native_sched_clock (60 samples, 0.01%)</title><rect x="1169.9" y="369" width="0.2" height="15.0" fill="rgb(233,205,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('list_del (47 samples, 0.01%)')" onmouseout="c()">
<title>list_del (47 samples, 0.01%)</title><rect x="761.7" y="113" width="0.1" height="15.0" fill="rgb(0,208,205)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__blockdev_direct_IO_newtrunc (61 samples, 0.01%)')" onmouseout="c()">
<title>__blockdev_direct_IO_newtrunc (61 samples, 0.01%)</title><rect x="11.0" y="273" width="0.2" height="15.0" fill="rgb(0,208,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_disable (2,912 samples, 0.66%)')" onmouseout="c()">
<title>perf_pmu_disable (2,912 samples, 0.66%)</title><rect x="202.8" y="209" width="7.8" height="15.0" fill="rgb(218,159,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_put (107 samples, 0.02%)')" onmouseout="c()">
<title>vmx_vcpu_put (107 samples, 0.02%)</title><rect x="689.2" y="305" width="0.3" height="15.0" fill="rgb(225,213,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cache_reap (306 samples, 0.07%)')" onmouseout="c()">
<title>cache_reap (306 samples, 0.07%)</title><rect x="979.3" y="417" width="0.8" height="15.0" fill="rgb(236,198,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (81 samples, 0.02%)')" onmouseout="c()">
<title>lock_hrtimer_base (81 samples, 0.02%)</title><rect x="1144.9" y="385" width="0.2" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('touch_softlockup_watchdog (93 samples, 0.02%)')" onmouseout="c()">
<title>touch_softlockup_watchdog (93 samples, 0.02%)</title><rect x="1041.9" y="337" width="0.3" height="15.0" fill="rgb(243,131,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('follow_page (212 samples, 0.05%)')" onmouseout="c()">
<title>follow_page (212 samples, 0.05%)</title><rect x="757.5" y="193" width="0.6" height="15.0" fill="rgb(0,226,139)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('seq_printf (110 samples, 0.02%)')" onmouseout="c()">
<title>seq_printf (110 samples, 0.02%)</title><rect x="973.0" y="337" width="0.2" height="15.0" fill="rgb(218,83,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_nohz_load_balancer (39 samples, 0.01%)')" onmouseout="c()">
<title>select_nohz_load_balancer (39 samples, 0.01%)</title><rect x="1124.9" y="433" width="0.1" height="15.0" fill="rgb(224,12,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tr_desc (247 samples, 0.06%)')" onmouseout="c()">
<title>native_load_tr_desc (247 samples, 0.06%)</title><rect x="686.3" y="257" width="0.7" height="15.0" fill="rgb(229,52,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tun_sendmsg (41 samples, 0.01%)')" onmouseout="c()">
<title>tun_sendmsg (41 samples, 0.01%)</title><rect x="977.2" y="369" width="0.1" height="15.0" fill="rgb(0,198,76)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (94 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock (94 samples, 0.02%)</title><rect x="152.1" y="241" width="0.3" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_idle (89 samples, 0.02%)')" onmouseout="c()">
<title>tick_check_idle (89 samples, 0.02%)</title><rect x="1176.5" y="321" width="0.2" height="15.0" fill="rgb(235,88,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_has_pending_timer (41 samples, 0.01%)')" onmouseout="c()">
<title>apic_has_pending_timer (41 samples, 0.01%)</title><rect x="610.1" y="305" width="0.1" height="15.0" fill="rgb(248,212,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_function_single_interrupt (141 samples, 0.03%)')" onmouseout="c()">
<title>call_function_single_interrupt (141 samples, 0.03%)</title><rect x="1046.4" y="417" width="0.4" height="15.0" fill="rgb(233,44,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_process_callbacks (717 samples, 0.16%)')" onmouseout="c()">
<title>rcu_process_callbacks (717 samples, 0.16%)</title><rect x="402.8" y="273" width="1.9" height="15.0" fill="rgb(209,24,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (83 samples, 0.02%)')" onmouseout="c()">
<title>tick_dev_program_event (83 samples, 0.02%)</title><rect x="1188.9" y="289" width="0.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ns_to_timespec (76 samples, 0.02%)')" onmouseout="c()">
<title>ns_to_timespec (76 samples, 0.02%)</title><rect x="1105.7" y="417" width="0.2" height="15.0" fill="rgb(227,143,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (50 samples, 0.01%)')" onmouseout="c()">
<title>enqueue_hrtimer (50 samples, 0.01%)</title><rect x="1189.3" y="337" width="0.1" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_posix_cpu_timers (1,256 samples, 0.28%)')" onmouseout="c()">
<title>run_posix_cpu_timers (1,256 samples, 0.28%)</title><rect x="178.6" y="257" width="3.3" height="15.0" fill="rgb(243,97,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (93 samples, 0.02%)')" onmouseout="c()">
<title>native_write_msr_safe (93 samples, 0.02%)</title><rect x="1046.5" y="241" width="0.2" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (1,206 samples, 0.27%)')" onmouseout="c()">
<title>tick_dev_program_event (1,206 samples, 0.27%)</title><rect x="1033.1" y="353" width="3.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (597 samples, 0.13%)')" onmouseout="c()">
<title>vmcs_writel (597 samples, 0.13%)</title><rect x="729.3" y="321" width="1.5" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('exit_intr (64 samples, 0.01%)')" onmouseout="c()">
<title>exit_intr (64 samples, 0.01%)</title><rect x="423.6" y="353" width="0.2" height="15.0" fill="rgb(229,76,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__direct_map (468 samples, 0.11%)')" onmouseout="c()">
<title>__direct_map (468 samples, 0.11%)</title><rect x="754.5" y="273" width="1.3" height="15.0" fill="rgb(0,212,121)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (1,198 samples, 0.27%)')" onmouseout="c()">
<title>apic_update_ppr (1,198 samples, 0.27%)</title><rect x="528.5" y="305" width="3.1" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_do_update_jiffies64 (76 samples, 0.02%)')" onmouseout="c()">
<title>tick_do_update_jiffies64 (76 samples, 0.02%)</title><rect x="1150.1" y="417" width="0.2" height="15.0" fill="rgb(236,208,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (212 samples, 0.05%)')" onmouseout="c()">
<title>idle_cpu (212 samples, 0.05%)</title><rect x="818.1" y="209" width="0.5" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtick_start_fair (85 samples, 0.02%)')" onmouseout="c()">
<title>hrtick_start_fair (85 samples, 0.02%)</title><rect x="647.4" y="257" width="0.3" height="15.0" fill="rgb(209,42,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (650 samples, 0.15%)')" onmouseout="c()">
<title>irq_exit (650 samples, 0.15%)</title><rect x="1043.4" y="385" width="1.7" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start_range_ns (3,619 samples, 0.82%)')" onmouseout="c()">
<title>hrtimer_start_range_ns (3,619 samples, 0.82%)</title><rect x="1138.5" y="417" width="9.6" height="15.0" fill="rgb(207,72,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__get_user_pages (2,200 samples, 0.50%)')" onmouseout="c()">
<title>__get_user_pages (2,200 samples, 0.50%)</title><rect x="757.4" y="209" width="5.8" height="15.0" fill="rgb(0,203,117)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (57,682 samples, 12.99%)')" onmouseout="c()">
<title>tick_sched_timer (57,682 samples, 12.99%)</title><rect x="137.5" y="289" width="153.3" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
<text text-anchor="" x="140.516064731917" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tick_sched_timer</text>
</g>
<g class="func_g" onmouseover="s('ext4_file_write (38 samples, 0.01%)')" onmouseout="c()">
<title>ext4_file_write (38 samples, 0.01%)</title><rect x="982.9" y="369" width="0.1" height="15.0" fill="rgb(205,10,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_entity_enqueue (460 samples, 0.10%)')" onmouseout="c()">
<title>account_entity_enqueue (460 samples, 0.10%)</title><rect x="1007.5" y="193" width="1.3" height="15.0" fill="rgb(224,51,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (81 samples, 0.02%)')" onmouseout="c()">
<title>activate_task (81 samples, 0.02%)</title><rect x="1000.3" y="273" width="0.2" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (57 samples, 0.01%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (57 samples, 0.01%)</title><rect x="641.9" y="209" width="0.2" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (84 samples, 0.02%)')" onmouseout="c()">
<title>save_args (84 samples, 0.02%)</title><rect x="1109.1" y="417" width="0.2" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (413 samples, 0.09%)')" onmouseout="c()">
<title>sched_clock (413 samples, 0.09%)</title><rect x="282.4" y="225" width="1.1" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (10,191 samples, 2.30%)')" onmouseout="c()">
<title>tick_dev_program_event (10,191 samples, 2.30%)</title><rect x="316.4" y="289" width="27.1" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
<text text-anchor="" x="319.430307330203" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s('update_shares (308 samples, 0.07%)')" onmouseout="c()">
<title>update_shares (308 samples, 0.07%)</title><rect x="1107.8" y="273" width="0.8" height="15.0" fill="rgb(229,50,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (3,117 samples, 0.70%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (3,117 samples, 0.70%)</title><rect x="1129.8" y="401" width="8.3" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_restart_sched_tick (76 samples, 0.02%)')" onmouseout="c()">
<title>tick_nohz_restart_sched_tick (76 samples, 0.02%)</title><rect x="1171.6" y="449" width="0.2" height="15.0" fill="rgb(210,23,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_ipi (253 samples, 0.06%)')" onmouseout="c()">
<title>scheduler_ipi (253 samples, 0.06%)</title><rect x="1184.4" y="337" width="0.7" height="15.0" fill="rgb(221,38,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_rebalance_domains (712 samples, 0.16%)')" onmouseout="c()">
<title>run_rebalance_domains (712 samples, 0.16%)</title><rect x="1106.8" y="305" width="1.8" height="15.0" fill="rgb(234,111,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (39 samples, 0.01%)')" onmouseout="c()">
<title>rb_erase (39 samples, 0.01%)</title><rect x="1187.1" y="321" width="0.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_tx (48 samples, 0.01%)')" onmouseout="c()">
<title>handle_tx (48 samples, 0.01%)</title><rect x="977.2" y="385" width="0.1" height="15.0" fill="rgb(0,237,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_sync_from_vapic (5,333 samples, 1.20%)')" onmouseout="c()">
<title>kvm_lapic_sync_from_vapic (5,333 samples, 1.20%)</title><rect x="560.8" y="337" width="14.1" height="15.0" fill="rgb(239,223,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (1,239 samples, 0.28%)')" onmouseout="c()">
<title>thread_return (1,239 samples, 0.28%)</title><rect x="1125.0" y="433" width="3.3" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_reg_write (14,140 samples, 3.18%)')" onmouseout="c()">
<title>apic_reg_write (14,140 samples, 3.18%)</title><rect x="783.6" y="257" width="37.6" height="15.0" fill="rgb(252,104,44)" rx="2" ry="2" />
<text text-anchor="" x="786.635730936856" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >api..</text>
</g>
<g class="func_g" onmouseover="s('rb_insert_color (283 samples, 0.06%)')" onmouseout="c()">
<title>rb_insert_color (283 samples, 0.06%)</title><rect x="1009.5" y="193" width="0.8" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (753 samples, 0.17%)')" onmouseout="c()">
<title>pit_has_pending_timer (753 samples, 0.17%)</title><rect x="536.8" y="321" width="2.0" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_cpu (320 samples, 0.07%)')" onmouseout="c()">
<title>sched_clock_cpu (320 samples, 0.07%)</title><rect x="1021.2" y="209" width="0.9" height="15.0" fill="rgb(232,83,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_select (1,223 samples, 0.28%)')" onmouseout="c()">
<title>menu_select (1,223 samples, 0.28%)</title><rect x="1102.3" y="417" width="3.3" height="15.0" fill="rgb(249,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_vcpu_kick (424 samples, 0.10%)')" onmouseout="c()">
<title>kvm_vcpu_kick (424 samples, 0.10%)</title><rect x="551.0" y="273" width="1.1" height="15.0" fill="rgb(217,38,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ipmi_thread (541 samples, 0.12%)')" onmouseout="c()">
<title>ipmi_thread (541 samples, 0.12%)</title><rect x="977.5" y="433" width="1.4" height="15.0" fill="rgb(254,34,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_handle_exit (1,484 samples, 0.33%)')" onmouseout="c()">
<title>vmx_handle_exit (1,484 samples, 0.33%)</title><rect x="931.0" y="353" width="4.0" height="15.0" fill="rgb(237,179,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ext4_da_write_end (83 samples, 0.02%)')" onmouseout="c()">
<title>ext4_da_write_end (83 samples, 0.02%)</title><rect x="974.4" y="273" width="0.2" height="15.0" fill="rgb(246,221,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_wrmsr (21,241 samples, 4.78%)')" onmouseout="c()">
<title>handle_wrmsr (21,241 samples, 4.78%)</title><rect x="767.7" y="321" width="56.4" height="15.0" fill="rgb(219,72,31)" rx="2" ry="2" />
<text text-anchor="" x="770.657353288963" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >handl..</text>
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (302 samples, 0.07%)')" onmouseout="c()">
<title>native_write_msr_safe (302 samples, 0.07%)</title><rect x="838.7" y="321" width="0.9" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('system_call_fastpath (95 samples, 0.02%)')" onmouseout="c()">
<title>system_call_fastpath (95 samples, 0.02%)</title><rect x="971.9" y="449" width="0.3" height="15.0" fill="rgb(224,162,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_tick (470 samples, 0.11%)')" onmouseout="c()">
<title>scheduler_tick (470 samples, 0.11%)</title><rect x="149.0" y="273" width="1.3" height="15.0" fill="rgb(240,30,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_wait (318 samples, 0.07%)')" onmouseout="c()">
<title>finish_wait (318 samples, 0.07%)</title><rect x="602.9" y="321" width="0.9" height="15.0" fill="rgb(215,130,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_process_times (155 samples, 0.03%)')" onmouseout="c()">
<title>update_process_times (155 samples, 0.03%)</title><rect x="290.8" y="289" width="0.4" height="15.0" fill="rgb(209,13,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock_idle_sleep_event (162 samples, 0.04%)')" onmouseout="c()">
<title>sched_clock_idle_sleep_event (162 samples, 0.04%)</title><rect x="1169.7" y="417" width="0.4" height="15.0" fill="rgb(247,203,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_timer_fn (182 samples, 0.04%)')" onmouseout="c()">
<title>kvm_timer_fn (182 samples, 0.04%)</title><rect x="1031.7" y="369" width="0.5" height="15.0" fill="rgb(249,104,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (111 samples, 0.03%)')" onmouseout="c()">
<title>tick_program_event (111 samples, 0.03%)</title><rect x="1176.0" y="321" width="0.3" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_pending_timer (2,701 samples, 0.61%)')" onmouseout="c()">
<title>kvm_cpu_has_pending_timer (2,701 samples, 0.61%)</title><rect x="531.6" y="337" width="7.2" height="15.0" fill="rgb(221,78,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_preempt_curr (407 samples, 0.09%)')" onmouseout="c()">
<title>check_preempt_curr (407 samples, 0.09%)</title><rect x="1022.4" y="257" width="1.1" height="15.0" fill="rgb(211,70,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_oneshot_broadcast (66 samples, 0.01%)')" onmouseout="c()">
<title>tick_check_oneshot_broadcast (66 samples, 0.01%)</title><rect x="1039.8" y="353" width="0.2" height="15.0" fill="rgb(232,124,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_update_ppr (157 samples, 0.04%)')" onmouseout="c()">
<title>apic_update_ppr (157 samples, 0.04%)</title><rect x="566.8" y="305" width="0.4" height="15.0" fill="rgb(244,88,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pm_qos_requirement (50 samples, 0.01%)')" onmouseout="c()">
<title>pm_qos_requirement (50 samples, 0.01%)</title><rect x="1106.2" y="417" width="0.1" height="15.0" fill="rgb(235,16,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_load (42 samples, 0.01%)')" onmouseout="c()">
<title>update_cfs_load (42 samples, 0.01%)</title><rect x="392.4" y="209" width="0.1" height="15.0" fill="rgb(242,121,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('madvise (43 samples, 0.01%)')" onmouseout="c()">
<title>madvise (43 samples, 0.01%)</title><rect x="980.6" y="465" width="0.1" height="15.0" fill="rgb(209,95,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cr8_intercept (599 samples, 0.13%)')" onmouseout="c()">
<title>update_cr8_intercept (599 samples, 0.13%)</title><rect x="727.7" y="321" width="1.6" height="15.0" fill="rgb(242,70,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (197 samples, 0.04%)')" onmouseout="c()">
<title>call_softirq (197 samples, 0.04%)</title><rect x="1127.2" y="353" width="0.5" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_local_bh_enable (57 samples, 0.01%)')" onmouseout="c()">
<title>_local_bh_enable (57 samples, 0.01%)</title><rect x="992.0" y="385" width="0.1" height="15.0" fill="rgb(210,228,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_to_user (165 samples, 0.04%)')" onmouseout="c()">
<title>copy_to_user (165 samples, 0.04%)</title><rect x="568.3" y="321" width="0.5" height="15.0" fill="rgb(206,70,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_IO_vsscanf (103 samples, 0.02%)')" onmouseout="c()">
<title>_IO_vsscanf (103 samples, 0.02%)</title><rect x="971.4" y="465" width="0.3" height="15.0" fill="rgb(239,121,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rb_rotate_left (87 samples, 0.02%)')" onmouseout="c()">
<title>__rb_rotate_left (87 samples, 0.02%)</title><rect x="1164.8" y="353" width="0.2" height="15.0" fill="rgb(251,185,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (199 samples, 0.04%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (199 samples, 0.04%)</title><rect x="789.2" y="209" width="0.5" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_pending_timer_irqs (406 samples, 0.09%)')" onmouseout="c()">
<title>kvm_inject_pending_timer_irqs (406 samples, 0.09%)</title><rect x="898.6" y="353" width="1.0" height="15.0" fill="rgb(216,161,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_preempt_curr (44 samples, 0.01%)')" onmouseout="c()">
<title>check_preempt_curr (44 samples, 0.01%)</title><rect x="798.1" y="97" width="0.1" height="15.0" fill="rgb(211,70,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__do_softirq (91 samples, 0.02%)')" onmouseout="c()">
<title>__do_softirq (91 samples, 0.02%)</title><rect x="356.8" y="289" width="0.2" height="15.0" fill="rgb(208,218,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (229 samples, 0.05%)')" onmouseout="c()">
<title>update_cfs_shares (229 samples, 0.05%)</title><rect x="276.4" y="241" width="0.6" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (226 samples, 0.05%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (226 samples, 0.05%)</title><rect x="1186.8" y="353" width="0.6" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule_hrtimeout_range (39 samples, 0.01%)')" onmouseout="c()">
<title>schedule_hrtimeout_range (39 samples, 0.01%)</title><rect x="970.6" y="353" width="0.1" height="15.0" fill="rgb(0,225,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (91 samples, 0.02%)')" onmouseout="c()">
<title>update_rq_clock (91 samples, 0.02%)</title><rect x="290.6" y="257" width="0.2" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_apic_mem_write (116 samples, 0.03%)')" onmouseout="c()">
<title>native_apic_mem_write (116 samples, 0.03%)</title><rect x="990.8" y="401" width="0.3" height="15.0" fill="rgb(209,177,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('sched_clock (134 samples, 0.03%)')" onmouseout="c()">
<title>sched_clock (134 samples, 0.03%)</title><rect x="649.5" y="241" width="0.4" height="15.0" fill="rgb(207,104,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmcs_writel (136 samples, 0.03%)')" onmouseout="c()">
<title>vmcs_writel (136 samples, 0.03%)</title><rect x="832.1" y="321" width="0.4" height="15.0" fill="rgb(228,19,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_sp0 (158 samples, 0.04%)')" onmouseout="c()">
<title>native_load_sp0 (158 samples, 0.04%)</title><rect x="980.9" y="465" width="0.4" height="15.0" fill="rgb(243,63,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('idle_cpu (935 samples, 0.21%)')" onmouseout="c()">
<title>idle_cpu (935 samples, 0.21%)</title><rect x="345.8" y="305" width="2.5" height="15.0" fill="rgb(231,163,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (216 samples, 0.05%)')" onmouseout="c()">
<title>call_softirq (216 samples, 0.05%)</title><rect x="914.5" y="289" width="0.6" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (190 samples, 0.04%)')" onmouseout="c()">
<title>update_curr (190 samples, 0.04%)</title><rect x="280.6" y="241" width="0.5" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__rcu_pending (609 samples, 0.14%)')" onmouseout="c()">
<title>__rcu_pending (609 samples, 0.14%)</title><rect x="172.6" y="241" width="1.7" height="15.0" fill="rgb(252,143,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (858 samples, 0.19%)')" onmouseout="c()">
<title>rb_next (858 samples, 0.19%)</title><rect x="113.0" y="273" width="2.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (276 samples, 0.06%)')" onmouseout="c()">
<title>rb_erase (276 samples, 0.06%)</title><rect x="997.6" y="337" width="0.7" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_from_user (83 samples, 0.02%)')" onmouseout="c()">
<title>copy_from_user (83 samples, 0.02%)</title><rect x="593.7" y="321" width="0.2" height="15.0" fill="rgb(237,136,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (964 samples, 0.22%)')" onmouseout="c()">
<title>tick_dev_program_event (964 samples, 0.22%)</title><rect x="1158.5" y="337" width="2.5" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__remove_hrtimer (44 samples, 0.01%)')" onmouseout="c()">
<title>__remove_hrtimer (44 samples, 0.01%)</title><rect x="1129.7" y="401" width="0.1" height="15.0" fill="rgb(244,9,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('preempt_notifier_unregister (44 samples, 0.01%)')" onmouseout="c()">
<title>preempt_notifier_unregister (44 samples, 0.01%)</title><rect x="689.0" y="305" width="0.1" height="15.0" fill="rgb(249,30,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_sched_tick (7,607 samples, 1.71%)')" onmouseout="c()">
<title>tick_nohz_stop_sched_tick (7,607 samples, 1.71%)</title><rect x="1150.5" y="433" width="20.2" height="15.0" fill="rgb(217,57,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_guest_get_msrs (1,498 samples, 0.34%)')" onmouseout="c()">
<title>intel_guest_get_msrs (1,498 samples, 0.34%)</title><rect x="885.1" y="305" width="4.0" height="15.0" fill="rgb(223,196,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('atomic_notifier_call_chain (59 samples, 0.01%)')" onmouseout="c()">
<title>atomic_notifier_call_chain (59 samples, 0.01%)</title><rect x="985.1" y="433" width="0.2" height="15.0" fill="rgb(249,80,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (41 samples, 0.01%)')" onmouseout="c()">
<title>_spin_lock (41 samples, 0.01%)</title><rect x="998.6" y="353" width="0.1" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_pmu_enable (47 samples, 0.01%)')" onmouseout="c()">
<title>perf_pmu_enable (47 samples, 0.01%)</title><rect x="216.2" y="225" width="0.1" height="15.0" fill="rgb(238,51,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_inject_pending_timer_irqs (4,539 samples, 1.02%)')" onmouseout="c()">
<title>kvm_inject_pending_timer_irqs (4,539 samples, 1.02%)</title><rect x="541.6" y="337" width="12.1" height="15.0" fill="rgb(216,161,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (79 samples, 0.02%)')" onmouseout="c()">
<title>schedule (79 samples, 0.02%)</title><rect x="983.2" y="465" width="0.2" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (70 samples, 0.02%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (70 samples, 0.02%)</title><rect x="1129.2" y="417" width="0.2" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__GI___libc_read (414 samples, 0.09%)')" onmouseout="c()">
<title>__GI___libc_read (414 samples, 0.09%)</title><rect x="972.2" y="465" width="1.1" height="15.0" fill="rgb(248,1,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (528 samples, 0.12%)')" onmouseout="c()">
<title>lock_hrtimer_base (528 samples, 0.12%)</title><rect x="814.5" y="193" width="1.4" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_rq_clock (505 samples, 0.11%)')" onmouseout="c()">
<title>update_rq_clock (505 samples, 0.11%)</title><rect x="648.6" y="273" width="1.3" height="15.0" fill="rgb(227,206,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock_irq (67 samples, 0.02%)')" onmouseout="c()">
<title>_spin_lock_irq (67 samples, 0.02%)</title><rect x="1116.8" y="417" width="0.2" height="15.0" fill="rgb(221,92,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('read_tsc (1,078 samples, 0.24%)')" onmouseout="c()">
<title>read_tsc (1,078 samples, 0.24%)</title><rect x="300.5" y="305" width="2.8" height="15.0" fill="rgb(233,66,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_load_tls (104 samples, 0.02%)')" onmouseout="c()">
<title>native_load_tls (104 samples, 0.02%)</title><rect x="13.6" y="433" width="0.2" height="15.0" fill="rgb(235,161,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mark_page_dirty (88 samples, 0.02%)')" onmouseout="c()">
<title>mark_page_dirty (88 samples, 0.02%)</title><rect x="693.5" y="321" width="0.2" height="15.0" fill="rgb(223,37,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_guest_get_msrs (2,204 samples, 0.50%)')" onmouseout="c()">
<title>perf_guest_get_msrs (2,204 samples, 0.50%)</title><rect x="883.3" y="321" width="5.8" height="15.0" fill="rgb(219,77,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get_update_offsets (44 samples, 0.01%)')" onmouseout="c()">
<title>ktime_get_update_offsets (44 samples, 0.01%)</title><rect x="1045.1" y="385" width="0.2" height="15.0" fill="rgb(246,219,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('perf_adjust_freq_unthr_context (289 samples, 0.07%)')" onmouseout="c()">
<title>perf_adjust_freq_unthr_context (289 samples, 0.07%)</title><rect x="192.1" y="241" width="0.7" height="15.0" fill="rgb(241,147,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rcu_bh_qs (180 samples, 0.04%)')" onmouseout="c()">
<title>rcu_bh_qs (180 samples, 0.04%)</title><rect x="367.2" y="257" width="0.5" height="15.0" fill="rgb(238,81,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__local_bh_enable (77 samples, 0.02%)')" onmouseout="c()">
<title>__local_bh_enable (77 samples, 0.02%)</title><rect x="1036.8" y="369" width="0.2" height="15.0" fill="rgb(250,106,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__list_add (82 samples, 0.02%)')" onmouseout="c()">
<title>__list_add (82 samples, 0.02%)</title><rect x="614.7" y="305" width="0.2" height="15.0" fill="rgb(223,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__netif_receive_skb (49 samples, 0.01%)')" onmouseout="c()">
<title>__netif_receive_skb (49 samples, 0.01%)</title><rect x="914.8" y="161" width="0.1" height="15.0" fill="rgb(207,148,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_start (406 samples, 0.09%)')" onmouseout="c()">
<title>hrtimer_start (406 samples, 0.09%)</title><rect x="1188.6" y="369" width="1.1" height="15.0" fill="rgb(252,146,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (218 samples, 0.05%)')" onmouseout="c()">
<title>irq_exit (218 samples, 0.05%)</title><rect x="914.5" y="321" width="0.6" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('reschedule_interrupt (258 samples, 0.06%)')" onmouseout="c()">
<title>reschedule_interrupt (258 samples, 0.06%)</title><rect x="1184.4" y="369" width="0.7" height="15.0" fill="rgb(213,96,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('save_args (702 samples, 0.16%)')" onmouseout="c()">
<title>save_args (702 samples, 0.16%)</title><rect x="915.9" y="353" width="1.8" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (1,432 samples, 0.32%)')" onmouseout="c()">
<title>tick_program_event (1,432 samples, 0.32%)</title><rect x="1032.5" y="369" width="3.8" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_curr (73 samples, 0.02%)')" onmouseout="c()">
<title>update_curr (73 samples, 0.02%)</title><rect x="648.2" y="257" width="0.2" height="15.0" fill="rgb(218,93,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('nr_iowait_cpu (103 samples, 0.02%)')" onmouseout="c()">
<title>nr_iowait_cpu (103 samples, 0.02%)</title><rect x="1042.5" y="321" width="0.2" height="15.0" fill="rgb(208,117,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_ts_time_stats (215 samples, 0.05%)')" onmouseout="c()">
<title>update_ts_time_stats (215 samples, 0.05%)</title><rect x="1042.2" y="337" width="0.5" height="15.0" fill="rgb(229,229,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (96 samples, 0.02%)')" onmouseout="c()">
<title>tick_dev_program_event (96 samples, 0.02%)</title><rect x="1032.3" y="369" width="0.2" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gfn_to_hva (357 samples, 0.08%)')" onmouseout="c()">
<title>gfn_to_hva (357 samples, 0.08%)</title><rect x="595.6" y="305" width="1.0" height="15.0" fill="rgb(234,174,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (105 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (105 samples, 0.02%)</title><rect x="1135.3" y="369" width="0.3" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_next (71 samples, 0.02%)')" onmouseout="c()">
<title>rb_next (71 samples, 0.02%)</title><rect x="1122.4" y="369" width="0.2" height="15.0" fill="rgb(251,1,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__block_prepare_write (43 samples, 0.01%)')" onmouseout="c()">
<title>__block_prepare_write (43 samples, 0.01%)</title><rect x="974.1" y="225" width="0.1" height="15.0" fill="rgb(0,231,110)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('enqueue_hrtimer (3,827 samples, 0.86%)')" onmouseout="c()">
<title>enqueue_hrtimer (3,827 samples, 0.86%)</title><rect x="116.2" y="289" width="10.2" height="15.0" fill="rgb(251,179,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('gup_pud_range (53 samples, 0.01%)')" onmouseout="c()">
<title>gup_pud_range (53 samples, 0.01%)</title><rect x="756.3" y="225" width="0.2" height="15.0" fill="rgb(0,196,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (1,232 samples, 0.28%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (1,232 samples, 0.28%)</title><rect x="892.1" y="353" width="3.2" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('block_write_begin (48 samples, 0.01%)')" onmouseout="c()">
<title>block_write_begin (48 samples, 0.01%)</title><rect x="974.1" y="257" width="0.1" height="15.0" fill="rgb(0,201,128)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('schedule (1,787 samples, 0.40%)')" onmouseout="c()">
<title>schedule (1,787 samples, 0.40%)</title><rect x="616.2" y="321" width="4.7" height="15.0" fill="rgb(222,218,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_dev_program_event (46 samples, 0.01%)')" onmouseout="c()">
<title>tick_dev_program_event (46 samples, 0.01%)</title><rect x="1133.0" y="353" width="0.1" height="15.0" fill="rgb(218,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (310 samples, 0.07%)')" onmouseout="c()">
<title>ktime_get (310 samples, 0.07%)</title><rect x="126.7" y="289" width="0.8" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_run (10,645 samples, 2.40%)')" onmouseout="c()">
<title>vmx_vcpu_run (10,645 samples, 2.40%)</title><rect x="942.0" y="433" width="28.3" height="15.0" fill="rgb(235,49,53)" rx="2" ry="2" />
<text text-anchor="" x="944.963061815151" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (1,261 samples, 0.28%)')" onmouseout="c()">
<title>native_write_msr_safe (1,261 samples, 0.28%)</title><rect x="206.1" y="161" width="3.4" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_check_idle (39 samples, 0.01%)')" onmouseout="c()">
<title>tick_check_idle (39 samples, 0.01%)</title><rect x="1045.8" y="385" width="0.1" height="15.0" fill="rgb(235,88,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (89 samples, 0.02%)')" onmouseout="c()">
<title>clockevents_program_event (89 samples, 0.02%)</title><rect x="1032.8" y="353" width="0.3" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (982 samples, 0.22%)')" onmouseout="c()">
<title>native_read_tsc (982 samples, 0.22%)</title><rect x="699.1" y="337" width="2.6" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smp_apic_timer_interrupt (110 samples, 0.02%)')" onmouseout="c()">
<title>smp_apic_timer_interrupt (110 samples, 0.02%)</title><rect x="1109.3" y="417" width="0.3" height="15.0" fill="rgb(205,22,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fix_small_imbalance (41 samples, 0.01%)')" onmouseout="c()">
<title>fix_small_imbalance (41 samples, 0.01%)</title><rect x="670.9" y="305" width="0.1" height="15.0" fill="rgb(206,210,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_lock (166 samples, 0.04%)')" onmouseout="c()">
<title>_spin_lock (166 samples, 0.04%)</title><rect x="625.3" y="305" width="0.4" height="15.0" fill="rgb(207,37,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (3,695 samples, 0.83%)')" onmouseout="c()">
<title>tick_program_event (3,695 samples, 0.83%)</title><rect x="413.7" y="321" width="9.9" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('[unknown] (361,559 samples, 81.43%)')" onmouseout="c()">
<title>[unknown] (361,559 samples, 81.43%)</title><rect x="10.4" y="465" width="960.9" height="15.0" fill="rgb(229,75,51)" rx="2" ry="2" />
<text text-anchor="" x="13.3614536527135" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >[unknown]</text>
</g>
<g class="func_g" onmouseover="s('save_args (770 samples, 0.17%)')" onmouseout="c()">
<title>save_args (770 samples, 0.17%)</title><rect x="13.9" y="433" width="2.0" height="15.0" fill="rgb(249,138,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('intel_pmu_enable_all (684 samples, 0.15%)')" onmouseout="c()">
<title>intel_pmu_enable_all (684 samples, 0.15%)</title><rect x="211.3" y="161" width="1.8" height="15.0" fill="rgb(252,128,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (74 samples, 0.02%)')" onmouseout="c()">
<title>native_read_tsc (74 samples, 0.02%)</title><rect x="341.0" y="257" width="0.2" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('account_cfs_rq_runtime (527 samples, 0.12%)')" onmouseout="c()">
<title>account_cfs_rq_runtime (527 samples, 0.12%)</title><rect x="273.3" y="209" width="1.4" height="15.0" fill="rgb(230,19,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pit_has_pending_timer (164 samples, 0.04%)')" onmouseout="c()">
<title>pit_has_pending_timer (164 samples, 0.04%)</title><rect x="613.1" y="321" width="0.4" height="15.0" fill="rgb(253,0,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (1,912 samples, 0.43%)')" onmouseout="c()">
<title>update_cfs_shares (1,912 samples, 0.43%)</title><rect x="1011.8" y="193" width="5.1" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ps_read_process (78 samples, 0.02%)')" onmouseout="c()">
<title>ps_read_process (78 samples, 0.02%)</title><rect x="1172.2" y="417" width="0.2" height="15.0" fill="rgb(224,223,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_arch_vcpu_ioctl_run (176,115 samples, 39.67%)')" onmouseout="c()">
<title>kvm_arch_vcpu_ioctl_run (176,115 samples, 39.67%)</title><rect x="423.8" y="353" width="468.0" height="15.0" fill="rgb(233,14,15)" rx="2" ry="2" />
<text text-anchor="" x="426.768753448878" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm_arch_vcpu_ioctl_run</text>
</g>
<g class="func_g" onmouseover="s('lock_hrtimer_base (329 samples, 0.07%)')" onmouseout="c()">
<title>lock_hrtimer_base (329 samples, 0.07%)</title><rect x="791.9" y="225" width="0.8" height="15.0" fill="rgb(232,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__libc_start_main (412 samples, 0.09%)')" onmouseout="c()">
<title>__libc_start_main (412 samples, 0.09%)</title><rect x="973.9" y="465" width="1.1" height="15.0" fill="rgb(249,32,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('try_to_wake_up (44 samples, 0.01%)')" onmouseout="c()">
<title>try_to_wake_up (44 samples, 0.01%)</title><rect x="983.5" y="353" width="0.1" height="15.0" fill="rgb(248,130,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('calc_delta_mine (477 samples, 0.11%)')" onmouseout="c()">
<title>calc_delta_mine (477 samples, 0.11%)</title><rect x="274.7" y="209" width="1.2" height="15.0" fill="rgb(235,150,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hrtimer_try_to_cancel (384 samples, 0.09%)')" onmouseout="c()">
<title>hrtimer_try_to_cancel (384 samples, 0.09%)</title><rect x="793.3" y="241" width="1.0" height="15.0" fill="rgb(221,57,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_pending_timer (223 samples, 0.05%)')" onmouseout="c()">
<title>kvm_cpu_has_pending_timer (223 samples, 0.05%)</title><rect x="610.0" y="321" width="0.6" height="15.0" fill="rgb(221,78,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (46 samples, 0.01%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (46 samples, 0.01%)</title><rect x="1152.4" y="417" width="0.2" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('scheduler_ipi (784 samples, 0.18%)')" onmouseout="c()">
<title>scheduler_ipi (784 samples, 0.18%)</title><rect x="1106.7" y="385" width="2.1" height="15.0" fill="rgb(221,38,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('do_softirq (722 samples, 0.16%)')" onmouseout="c()">
<title>do_softirq (722 samples, 0.16%)</title><rect x="1106.7" y="353" width="2.0" height="15.0" fill="rgb(243,77,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__hrtimer_start_range_ns (395 samples, 0.09%)')" onmouseout="c()">
<title>__hrtimer_start_range_ns (395 samples, 0.09%)</title><rect x="1188.6" y="353" width="1.0" height="15.0" fill="rgb(250,176,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_present (112 samples, 0.03%)')" onmouseout="c()">
<title>kvm_apic_present (112 samples, 0.03%)</title><rect x="795.9" y="225" width="0.3" height="15.0" fill="rgb(244,129,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('check_for_new_grace_period (994 samples, 0.22%)')" onmouseout="c()">
<title>check_for_new_grace_period (994 samples, 0.22%)</title><rect x="378.1" y="225" width="2.6" height="15.0" fill="rgb(217,224,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__mem_cgroup_try_charge (54 samples, 0.01%)')" onmouseout="c()">
<title>__mem_cgroup_try_charge (54 samples, 0.01%)</title><rect x="762.4" y="129" width="0.2" height="15.0" fill="rgb(0,224,88)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('smi_event_handler (446 samples, 0.10%)')" onmouseout="c()">
<title>smi_event_handler (446 samples, 0.10%)</title><rect x="977.7" y="417" width="1.2" height="15.0" fill="rgb(222,16,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('run_local_timers (64 samples, 0.01%)')" onmouseout="c()">
<title>run_local_timers (64 samples, 0.01%)</title><rect x="148.3" y="273" width="0.2" height="15.0" fill="rgb(216,154,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (43 samples, 0.01%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (43 samples, 0.01%)</title><rect x="1154.0" y="385" width="0.1" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rebalance_domains (124 samples, 0.03%)')" onmouseout="c()">
<title>rebalance_domains (124 samples, 0.03%)</title><rect x="1127.3" y="305" width="0.4" height="15.0" fill="rgb(207,28,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_rt (169 samples, 0.04%)')" onmouseout="c()">
<title>pick_next_task_rt (169 samples, 0.04%)</title><rect x="671.4" y="305" width="0.4" height="15.0" fill="rgb(244,112,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ksoftirqd (43 samples, 0.01%)')" onmouseout="c()">
<title>ksoftirqd (43 samples, 0.01%)</title><rect x="979.0" y="433" width="0.1" height="15.0" fill="rgb(239,229,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_stop_sched_tick (633 samples, 0.14%)')" onmouseout="c()">
<title>tick_nohz_stop_sched_tick (633 samples, 0.14%)</title><rect x="1188.2" y="385" width="1.7" height="15.0" fill="rgb(217,57,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (203 samples, 0.05%)')" onmouseout="c()">
<title>rb_insert_color (203 samples, 0.05%)</title><rect x="815.9" y="193" width="0.6" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_put_guest_fpu (99 samples, 0.02%)')" onmouseout="c()">
<title>kvm_put_guest_fpu (99 samples, 0.02%)</title><rect x="688.5" y="305" width="0.2" height="15.0" fill="rgb(214,10,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (54 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (54 samples, 0.01%)</title><rect x="1099.5" y="369" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_sched_timer (158 samples, 0.04%)')" onmouseout="c()">
<title>tick_sched_timer (158 samples, 0.04%)</title><rect x="343.5" y="305" width="0.4" height="15.0" fill="rgb(213,15,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_read_tsc (43 samples, 0.01%)')" onmouseout="c()">
<title>native_read_tsc (43 samples, 0.01%)</title><rect x="685.4" y="273" width="0.1" height="15.0" fill="rgb(249,99,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_cpu_has_interrupt (162 samples, 0.04%)')" onmouseout="c()">
<title>kvm_cpu_has_interrupt (162 samples, 0.04%)</title><rect x="609.5" y="321" width="0.5" height="15.0" fill="rgb(245,130,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_program_event (102 samples, 0.02%)')" onmouseout="c()">
<title>tick_program_event (102 samples, 0.02%)</title><rect x="1188.9" y="305" width="0.2" height="15.0" fill="rgb(219,99,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('ktime_get (402 samples, 0.09%)')" onmouseout="c()">
<title>ktime_get (402 samples, 0.09%)</title><rect x="315.4" y="289" width="1.0" height="15.0" fill="rgb(213,13,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('activate_task (442 samples, 0.10%)')" onmouseout="c()">
<title>activate_task (442 samples, 0.10%)</title><rect x="1174.2" y="209" width="1.2" height="15.0" fill="rgb(233,36,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('timekeeping_update (190 samples, 0.04%)')" onmouseout="c()">
<title>timekeeping_update (190 samples, 0.04%)</title><rect x="153.1" y="241" width="0.5" height="15.0" fill="rgb(253,156,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('skip_emulated_instruction (214 samples, 0.05%)')" onmouseout="c()">
<title>skip_emulated_instruction (214 samples, 0.05%)</title><rect x="766.0" y="305" width="0.6" height="15.0" fill="rgb(220,204,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_set_eoi (562 samples, 0.13%)')" onmouseout="c()">
<title>apic_set_eoi (562 samples, 0.13%)</title><rect x="495.9" y="337" width="1.5" height="15.0" fill="rgb(250,72,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('native_write_msr_safe (55 samples, 0.01%)')" onmouseout="c()">
<title>native_write_msr_safe (55 samples, 0.01%)</title><rect x="688.3" y="273" width="0.2" height="15.0" fill="rgb(215,163,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('blk_done_softirq (54 samples, 0.01%)')" onmouseout="c()">
<title>blk_done_softirq (54 samples, 0.01%)</title><rect x="914.5" y="257" width="0.2" height="15.0" fill="rgb(0,220,204)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_is_periodic (141 samples, 0.03%)')" onmouseout="c()">
<title>lapic_is_periodic (141 samples, 0.03%)</title><rect x="1029.4" y="337" width="0.4" height="15.0" fill="rgb(205,14,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clockevents_program_event (1,903 samples, 0.43%)')" onmouseout="c()">
<title>clockevents_program_event (1,903 samples, 0.43%)</title><rect x="334.1" y="273" width="5.1" height="15.0" fill="rgb(233,130,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vhost_worker (106 samples, 0.02%)')" onmouseout="c()">
<title>vhost_worker (106 samples, 0.02%)</title><rect x="977.1" y="417" width="0.3" height="15.0" fill="rgb(253,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('vmx_vcpu_load (384 samples, 0.09%)')" onmouseout="c()">
<title>vmx_vcpu_load (384 samples, 0.09%)</title><rect x="681.8" y="289" width="1.0" height="15.0" fill="rgb(243,164,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('irq_exit (1,505 samples, 0.34%)')" onmouseout="c()">
<title>irq_exit (1,505 samples, 0.34%)</title><rect x="34.7" y="337" width="4.0" height="15.0" fill="rgb(216,86,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('clear_page_c (603 samples, 0.14%)')" onmouseout="c()">
<title>clear_page_c (603 samples, 0.14%)</title><rect x="759.4" y="129" width="1.6" height="15.0" fill="rgb(0,216,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('start_apic_timer (199 samples, 0.04%)')" onmouseout="c()">
<title>start_apic_timer (199 samples, 0.04%)</title><rect x="822.1" y="257" width="0.5" height="15.0" fill="rgb(251,224,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('thread_return (129 samples, 0.03%)')" onmouseout="c()">
<title>thread_return (129 samples, 0.03%)</title><rect x="941.6" y="433" width="0.4" height="15.0" fill="rgb(241,141,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('put_prev_task_idle (69 samples, 0.02%)')" onmouseout="c()">
<title>put_prev_task_idle (69 samples, 0.02%)</title><rect x="1112.9" y="433" width="0.2" height="15.0" fill="rgb(214,94,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('menu_select (86 samples, 0.02%)')" onmouseout="c()">
<title>menu_select (86 samples, 0.02%)</title><rect x="1112.3" y="433" width="0.2" height="15.0" fill="rgb(249,45,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_apic_local_deliver (435 samples, 0.10%)')" onmouseout="c()">
<title>kvm_apic_local_deliver (435 samples, 0.10%)</title><rect x="541.7" y="321" width="1.1" height="15.0" fill="rgb(221,119,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('_spin_unlock_irqrestore (155 samples, 0.03%)')" onmouseout="c()">
<title>_spin_unlock_irqrestore (155 samples, 0.03%)</title><rect x="675.6" y="289" width="0.5" height="15.0" fill="rgb(250,72,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('select_task_rq_fair (60 samples, 0.01%)')" onmouseout="c()">
<title>select_task_rq_fair (60 samples, 0.01%)</title><rect x="1175.5" y="209" width="0.1" height="15.0" fill="rgb(210,171,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_load_guest_fpu (787 samples, 0.18%)')" onmouseout="c()">
<title>kvm_load_guest_fpu (787 samples, 0.18%)</title><rect x="591.2" y="337" width="2.1" height="15.0" fill="rgb(222,228,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_stats_wait_end (511 samples, 0.12%)')" onmouseout="c()">
<title>update_stats_wait_end (511 samples, 0.12%)</title><rect x="1122.9" y="385" width="1.4" height="15.0" fill="rgb(253,139,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_get_apic_interrupt (3,029 samples, 0.68%)')" onmouseout="c()">
<title>kvm_get_apic_interrupt (3,029 samples, 0.68%)</title><rect x="506.4" y="321" width="8.0" height="15.0" fill="rgb(252,74,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm_lapic_enabled (826 samples, 0.19%)')" onmouseout="c()">
<title>kvm_lapic_enabled (826 samples, 0.19%)</title><rect x="899.6" y="353" width="2.2" height="15.0" fill="rgb(211,200,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('main (412 samples, 0.09%)')" onmouseout="c()">
<title>main (412 samples, 0.09%)</title><rect x="973.9" y="449" width="1.1" height="15.0" fill="rgb(210,111,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('skip_emulated_instruction (444 samples, 0.10%)')" onmouseout="c()">
<title>skip_emulated_instruction (444 samples, 0.10%)</title><rect x="826.6" y="321" width="1.2" height="15.0" fill="rgb(220,204,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('copy_to_user (479 samples, 0.11%)')" onmouseout="c()">
<title>copy_to_user (479 samples, 0.11%)</title><rect x="581.3" y="321" width="1.3" height="15.0" fill="rgb(206,70,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_insert_color (129 samples, 0.03%)')" onmouseout="c()">
<title>rb_insert_color (129 samples, 0.03%)</title><rect x="1019.3" y="209" width="0.4" height="15.0" fill="rgb(222,158,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('tick_nohz_get_sleep_length (60 samples, 0.01%)')" onmouseout="c()">
<title>tick_nohz_get_sleep_length (60 samples, 0.01%)</title><rect x="1109.7" y="417" width="0.1" height="15.0" fill="rgb(227,96,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('port_inb (419 samples, 0.09%)')" onmouseout="c()">
<title>port_inb (419 samples, 0.09%)</title><rect x="977.8" y="385" width="1.1" height="15.0" fill="rgb(251,85,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_ts_time_stats (65 samples, 0.01%)')" onmouseout="c()">
<title>update_ts_time_stats (65 samples, 0.01%)</title><rect x="1042.8" y="353" width="0.1" height="15.0" fill="rgb(229,229,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('rb_erase (40 samples, 0.01%)')" onmouseout="c()">
<title>rb_erase (40 samples, 0.01%)</title><rect x="1189.5" y="337" width="0.1" height="15.0" fill="rgb(236,5,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('handle_halt (339 samples, 0.08%)')" onmouseout="c()">
<title>handle_halt (339 samples, 0.08%)</title><rect x="765.8" y="321" width="0.9" height="15.0" fill="rgb(226,182,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__wake_up_common (72 samples, 0.02%)')" onmouseout="c()">
<title>__wake_up_common (72 samples, 0.02%)</title><rect x="766.7" y="225" width="0.2" height="15.0" fill="rgb(214,94,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('find_next_bit (652 samples, 0.15%)')" onmouseout="c()">
<title>find_next_bit (652 samples, 0.15%)</title><rect x="666.0" y="273" width="1.7" height="15.0" fill="rgb(231,134,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_vsyscall (98 samples, 0.02%)')" onmouseout="c()">
<title>update_vsyscall (98 samples, 0.02%)</title><rect x="153.4" y="225" width="0.2" height="15.0" fill="rgb(229,101,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('cpuidle_idle_call (4,680 samples, 1.05%)')" onmouseout="c()">
<title>cpuidle_idle_call (4,680 samples, 1.05%)</title><rect x="1173.0" y="385" width="12.4" height="15.0" fill="rgb(214,193,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('finish_task_switch (86 samples, 0.02%)')" onmouseout="c()">
<title>finish_task_switch (86 samples, 0.02%)</title><rect x="602.7" y="321" width="0.2" height="15.0" fill="rgb(221,162,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('call_softirq (514 samples, 0.12%)')" onmouseout="c()">
<title>call_softirq (514 samples, 0.12%)</title><rect x="353.3" y="305" width="1.4" height="15.0" fill="rgb(240,215,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pick_next_task_fair (2,526 samples, 0.57%)')" onmouseout="c()">
<title>pick_next_task_fair (2,526 samples, 0.57%)</title><rect x="1117.9" y="417" width="6.7" height="15.0" fill="rgb(241,43,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('block_write_begin_newtrunc (47 samples, 0.01%)')" onmouseout="c()">
<title>block_write_begin_newtrunc (47 samples, 0.01%)</title><rect x="974.1" y="241" width="0.1" height="15.0" fill="rgb(0,201,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('apic_reg_write (347 samples, 0.08%)')" onmouseout="c()">
<title>apic_reg_write (347 samples, 0.08%)</title><rect x="781.8" y="273" width="0.9" height="15.0" fill="rgb(252,104,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('__select (137 samples, 0.03%)')" onmouseout="c()">
<title>__select (137 samples, 0.03%)</title><rect x="970.4" y="449" width="0.4" height="15.0" fill="rgb(244,9,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('hva_to_pfn (90 samples, 0.02%)')" onmouseout="c()">
<title>hva_to_pfn (90 samples, 0.02%)</title><rect x="756.2" y="257" width="0.3" height="15.0" fill="rgb(0,194,206)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('lapic_next_event (353 samples, 0.08%)')" onmouseout="c()">
<title>lapic_next_event (353 samples, 0.08%)</title><rect x="342.0" y="273" width="0.9" height="15.0" fill="rgb(229,17,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('task_waking_fair (113 samples, 0.03%)')" onmouseout="c()">
<title>task_waking_fair (113 samples, 0.03%)</title><rect x="1000.8" y="273" width="0.3" height="15.0" fill="rgb(252,81,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('update_cfs_shares (2,863 samples, 0.64%)')" onmouseout="c()">
<title>update_cfs_shares (2,863 samples, 0.64%)</title><rect x="229.4" y="225" width="7.6" height="15.0" fill="rgb(240,28,1)" rx="2" ry="2" />
</g>
</svg>
