package logicCreate

import (
	"context"
	"errors"
	"usersrv/internal/config"
	daoCreate "usersrv/internal/dao/dao_create"
	"usersrv/internal/model"

	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
)

// CreatePlayer 创建账号
func CreatePlayer(ctx context.Context, pMasterInfo *model.PlayerMaster, playerInfo *model.PlayerInfo, pDeviceInfo *model.PlayerDevices) (uint64, error) {
	entry := logx.NewLogEntry(ctx)

	if pMasterInfo == nil || playerInfo == nil || pDeviceInfo == nil {
		entry.Errorf("create player failed, params error")
		return 0, errors.New("params error")
	}

	// 生成玩家ID
	if pMasterInfo.PlayerID == 0 {
		uid, err := GeneratePlayerID(ctx, pMasterInfo.ProductID)
		if err != nil {
			entry.Errorf("generate player id failed: %v", err)
			return 0, err
		}
		pMasterInfo.PlayerID = uid
	}

	// 先设置玩家信息到数据库
	playerId, err := daoCreate.CreatePlayerRdb(ctx, pMasterInfo, playerInfo, pDeviceInfo)
	if playerId <= 0 || err != nil {
		entry.Errorf("create player failed, playerId:%d, err:%v", playerId, err)
		return playerId, err
	}

	// 加锁创建玩家信息到redis
	CreatePlayerRds(ctx, pMasterInfo.ProductID, playerId, pMasterInfo, playerInfo, pDeviceInfo)

	return playerId, nil
}

// CreatePlayerRds 加锁创建玩家信息到redis
func CreatePlayerRds(ctx context.Context, productId int32, playerId uint64, pMasterInfo *model.PlayerMaster, playerInfo *model.PlayerInfo, pDeviceInfo *model.PlayerDevices) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId <= 0 || pMasterInfo == nil || playerInfo == nil || pDeviceInfo == nil {
		entry.Errorf("create player failed, params error, playerId:%d", playerId)
		return 0, errors.New("params error")
	}

	getLock, unLock := dlm.DefaultLockMgr.OptimisticLockKey(config.LockKeyVisitorLogin(pMasterInfo.ProductID, pMasterInfo.DeviceCode), int(config.LOCK_EXPIRE))
	if !getLock {
		entry.Errorf("create player failed, lock error")
		return 0, errors.New("other locked")
	}
	defer unLock()

	// 设置到redis
	daoCreate.CachePlayerInfoRds(ctx, productId, playerId, pMasterInfo, playerInfo, pDeviceInfo)

	return playerId, nil
}

// CreateProtoToModel 将proto数据转换为model数据
func CreateProtoToModel(proto *userRpc.CreatePlayerReq) (*model.PlayerMaster, *model.PlayerInfo, *model.PlayerDevices) {
	pMaster := model.NewPlayerMasterFromProto(proto)
	pInfo := model.NewPlayerInfoFromProto(proto)
	pDevice := model.NewDeviceInfoFromProto(proto)
	return pMaster, pInfo, pDevice
}
