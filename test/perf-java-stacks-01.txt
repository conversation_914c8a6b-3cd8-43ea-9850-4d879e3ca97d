# ========
# captured on: Sun Oct 25 23:31:43 2015
# hostname : lgud-bgregg
# os release : 3.13.0-44-generic
# perf version : 3.13.11-ckt12
# arch : x86_64
# nrcpus online : 4
# nrcpus avail : 4
# cpudesc : Intel(R) Core(TM) i5-2400 CPU @ 3.10GHz
# cpuid : GenuineIntel,6,42,7
# total memory : 4005800 kB
# cmdline : /usr/lib/linux-tools-3.13.0-44/perf record -F 99 -a -g -- sleep 30 
# event : name = cycles, type = 0, config = 0x0, config1 = 0x0, config2 = 0x0, excl_usr = 0, excl_kern = 0, excl_host = 0, excl_guest = 1, precise_ip = 0, attr_mmap2 = 0, attr_mmap  = 1, attr_mmap_data = 0
# HEADER_CPU_TOPOLOGY info available, use -I to display
# HEADER_NUMA_TOPOLOGY info available, use -I to display
# pmu mappings: cpu = 4, software = 1, tracepoint = 2, uncore_cbox_0 = 6, uncore_cbox_1 = 7, uncore_cbox_2 = 8, uncore_cbox_3 = 9, breakpoint = 5
# ========
#
ab 23927 [000] 184694.229089: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cab4c0b0 [unknown] ([unknown])

ab 23927 [000] 184694.229097: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cab4c0b0 [unknown] ([unknown])

ab 23927 [000] 184694.229099: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cab4c0b0 [unknown] ([unknown])

ab 23927 [000] 184694.229101: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cab4c0b0 [unknown] ([unknown])

java 23921 [001] 184694.229109: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d259b1d Lorg/mozilla/javascript/WrapFactory;.wrap (/tmp/perf-23895.map)
	    7f6b2d329384 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [001] 184694.229114: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d259b1d Lorg/mozilla/javascript/WrapFactory;.wrap (/tmp/perf-23895.map)
	    7f6b2d329384 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [001] 184694.229117: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d259b1d Lorg/mozilla/javascript/WrapFactory;.wrap (/tmp/perf-23895.map)
	    7f6b2d329384 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [001] 184694.229119: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d259b1d Lorg/mozilla/javascript/WrapFactory;.wrap (/tmp/perf-23895.map)
	    7f6b2d329384 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

perf 23929 [002] 184694.229126: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dbfef smp_call_function_single ([kernel.kallsyms])
	ffffffff8113e044 cpu_function_call ([kernel.kallsyms])
	ffffffff811419f5 perf_event_enable ([kernel.kallsyms])
	ffffffff8113e0d8 perf_event_for_each_child ([kernel.kallsyms])
	ffffffff81141b1b perf_ioctl ([kernel.kallsyms])
	ffffffff811d0e80 do_vfs_ioctl ([kernel.kallsyms])
	ffffffff811d10e1 sys_ioctl ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f2ccba26ec7 __GI___ioctl (/lib/x86_64-linux-gnu/libc-2.19.so)
	          415ad5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          4081a5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          407a40 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	    7f2ccb956ec5 __libc_start_main (/lib/x86_64-linux-gnu/libc-2.19.so)

swapper     0 [000] 184694.229130: cycles: 
	ffffffff810c8c9e rcu_idle_enter ([kernel.kallsyms])
	ffffffff810bef30 cpu_startup_entry ([kernel.kallsyms])
	ffffffff8170f247 rest_init ([kernel.kallsyms])
	ffffffff81d35f70 start_kernel ([kernel.kallsyms])
	ffffffff81d355ee x86_64_start_reservations ([kernel.kallsyms])
	ffffffff81d35733 x86_64_start_kernel ([kernel.kallsyms])

perf 23929 [002] 184694.229130: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dbfef smp_call_function_single ([kernel.kallsyms])
	ffffffff8113e044 cpu_function_call ([kernel.kallsyms])
	ffffffff811419f5 perf_event_enable ([kernel.kallsyms])
	ffffffff8113e0d8 perf_event_for_each_child ([kernel.kallsyms])
	ffffffff81141b1b perf_ioctl ([kernel.kallsyms])
	ffffffff811d0e80 do_vfs_ioctl ([kernel.kallsyms])
	ffffffff811d10e1 sys_ioctl ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f2ccba26ec7 __GI___ioctl (/lib/x86_64-linux-gnu/libc-2.19.so)
	          415ad5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          4081a5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          407a40 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	    7f2ccb956ec5 __libc_start_main (/lib/x86_64-linux-gnu/libc-2.19.so)

perf 23929 [002] 184694.229133: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dbfef smp_call_function_single ([kernel.kallsyms])
	ffffffff8113e044 cpu_function_call ([kernel.kallsyms])
	ffffffff811419f5 perf_event_enable ([kernel.kallsyms])
	ffffffff8113e0d8 perf_event_for_each_child ([kernel.kallsyms])
	ffffffff81141b1b perf_ioctl ([kernel.kallsyms])
	ffffffff811d0e80 do_vfs_ioctl ([kernel.kallsyms])
	ffffffff811d10e1 sys_ioctl ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f2ccba26ec7 __GI___ioctl (/lib/x86_64-linux-gnu/libc-2.19.so)
	          415ad5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          4081a5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          407a40 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	    7f2ccb956ec5 __libc_start_main (/lib/x86_64-linux-gnu/libc-2.19.so)

perf 23929 [002] 184694.229135: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dbfef smp_call_function_single ([kernel.kallsyms])
	ffffffff8113e044 cpu_function_call ([kernel.kallsyms])
	ffffffff811419f5 perf_event_enable ([kernel.kallsyms])
	ffffffff8113e0d8 perf_event_for_each_child ([kernel.kallsyms])
	ffffffff81141b1b perf_ioctl ([kernel.kallsyms])
	ffffffff811d0e80 do_vfs_ioctl ([kernel.kallsyms])
	ffffffff811d10e1 sys_ioctl ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f2ccba26ec7 __GI___ioctl (/lib/x86_64-linux-gnu/libc-2.19.so)
	          415ad5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          4081a5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          407a40 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	    7f2ccb956ec5 __libc_start_main (/lib/x86_64-linux-gnu/libc-2.19.so)

java 23923 [003] 184694.229142: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d0bbad0 Lorg/mozilla/javascript/TopLevel;.getBuiltinPrototype (/tmp/perf-23895.map)
	    7f6b2d0c8544 Lorg/mozilla/javascript/NativeFunction;.initScriptFunction (/tmp/perf-23895.map)
	    7f6b2d1dd178 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.<init> (/tmp/perf-23895.map)
	    7f6b2d595d2c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [003] 184694.229148: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d0bbad0 Lorg/mozilla/javascript/TopLevel;.getBuiltinPrototype (/tmp/perf-23895.map)
	    7f6b2d0c8544 Lorg/mozilla/javascript/NativeFunction;.initScriptFunction (/tmp/perf-23895.map)
	    7f6b2d1dd178 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.<init> (/tmp/perf-23895.map)
	    7f6b2d595d2c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [003] 184694.229150: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d0bbad0 Lorg/mozilla/javascript/TopLevel;.getBuiltinPrototype (/tmp/perf-23895.map)
	    7f6b2d0c8544 Lorg/mozilla/javascript/NativeFunction;.initScriptFunction (/tmp/perf-23895.map)
	    7f6b2d1dd178 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.<init> (/tmp/perf-23895.map)
	    7f6b2d595d2c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [003] 184694.229153: cycles: 
	ffffffff8104f45a native_write_msr_safe ([kernel.kallsyms])
	ffffffff8102f8bc intel_pmu_enable_all ([kernel.kallsyms])
	ffffffff81029b14 x86_pmu_enable ([kernel.kallsyms])
	ffffffff81142a77 perf_pmu_enable ([kernel.kallsyms])
	ffffffff81027bfa x86_pmu_commit_txn ([kernel.kallsyms])
	ffffffff811434f0 group_sched_in ([kernel.kallsyms])
	ffffffff811439b2 __perf_event_enable ([kernel.kallsyms])
	ffffffff8113f620 remote_function ([kernel.kallsyms])
	ffffffff810dc880 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81040ac7 smp_call_function_single_interrupt ([kernel.kallsyms])
	ffffffff81732a5d call_function_single_interrupt ([kernel.kallsyms])
	    7f6b2d0bbad0 Lorg/mozilla/javascript/TopLevel;.getBuiltinPrototype (/tmp/perf-23895.map)
	    7f6b2d0c8544 Lorg/mozilla/javascript/NativeFunction;.initScriptFunction (/tmp/perf-23895.map)
	    7f6b2d1dd178 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.<init> (/tmp/perf-23895.map)
	    7f6b2d595d2c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [001] 184694.229157: cycles: 
	    7f6b2d0bad15 Lorg/mozilla/javascript/ScriptableObject;.createSlot (/tmp/perf-23895.map)
	    7f6b2d0904a8 Lorg/mozilla/javascript/ScriptableObject;.getSlot (/tmp/perf-23895.map)
	    7f6b2d0bdc1c Lorg/mozilla/javascript/IdScriptableObject;.put (/tmp/perf-23895.map)
	    7f6b2d0ca488 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d5831dc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d41cac0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d41d15c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d41c9c4 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

perf 23929 [002] 184694.229184: cycles: 
	ffffffff81729600 page_fault ([kernel.kallsyms])
	          415c96 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          4081a5 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	          407a40 [unknown] (/usr/lib/linux-tools-3.13.0-44/perf)
	    7f2ccb956ec5 __libc_start_main (/lib/x86_64-linux-gnu/libc-2.19.so)

java 23923 [003] 184694.229188: cycles: 
	    7f6b2d0902c7 Lorg/mozilla/javascript/ScriptableObject;.getSlot (/tmp/perf-23895.map)
	    7f6b2d0ca4f4 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d5614a8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_47;.call (/tmp/perf-23895.map)
	    7f6b2d21679c Lorg/mozilla/javascript/optimizer/OptRuntime;.call2 (/tmp/perf-23895.map)
	    7f6b2d595ee4 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [000] 184694.243059: cycles: 
	    7f6b2d416e87 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.getParamCount (/tmp/perf-23895.map)
	    7f6b2d5946dc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [001] 184694.246484: cycles: 
	    7f6b2d3d11b0 Lio/netty/handler/codec/http/HttpMethod;.valueOf (/tmp/perf-23895.map)
	    7f6b2d3ffe24 Lio/netty/handler/codec/http/HttpObjectDecoder;.decode (/tmp/perf-23895.map)
	    7f6b2d417fd0 Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23922 [003] 184694.246867: cycles: 
	    7f6b2d0bad52 Lorg/mozilla/javascript/ScriptableObject;.createSlot (/tmp/perf-23895.map)
	    7f6b2d0904a8 Lorg/mozilla/javascript/ScriptableObject;.getSlot (/tmp/perf-23895.map)
	    7f6b2d0bdc1c Lorg/mozilla/javascript/IdScriptableObject;.put (/tmp/perf-23895.map)
	    7f6b2d0ca488 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d5b3fdc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d493078 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d49363c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d492f7c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [002] 184694.255900: cycles: 
	    7f6b2d54ff29 Lsun/nio/ch/SocketChannelImpl;.read (/tmp/perf-23895.map)
	    7f6b2d519374 Lio/netty/buffer/AbstractByteBuf;.writeBytes (/tmp/perf-23895.map)
	    7f6b2d55840c Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23920 [000] 184694.356511: cycles: 
	    7f6b2d329439 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23922 [003] 184694.363320: cycles: 
	    7f6b2d0d5c53 Ljava/lang/Thread;.blockedOn (/tmp/perf-23895.map)
	    7f6b2d550194 Lsun/nio/ch/SocketChannelImpl;.read (/tmp/perf-23895.map)
	    7f6b2d519374 Lio/netty/buffer/AbstractByteBuf;.writeBytes (/tmp/perf-23895.map)
	    7f6b2d55840c Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [001] 184694.365722: cycles: 
	    7f6b2d1f22c2 Lorg/mozilla/javascript/ScriptRuntime;.getPropFunctionAndThis (/tmp/perf-23895.map)
	    7f6b2d5838ec Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d41cac0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d41d15c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d41c9c4 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23920 [002] 184694.366480: cycles: 
	    7f6b2d0a4908 Lorg/mozilla/javascript/IdScriptableObject;.has (/tmp/perf-23895.map)
	    7f6b2d0ca4f4 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d536bb4 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_31;.call (/tmp/perf-23895.map)
	    7f6b2d21679c Lorg/mozilla/javascript/optimizer/OptRuntime;.call2 (/tmp/perf-23895.map)
	    7f6b2d5a7658 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80;._c_anonymous_21 (/tmp/perf-23895.map)
	    7f6b2d3d4158 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d59d6a0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d3d4080 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d3d472c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80;.call (/tmp/perf-23895.map)
	    7f6b2d3d3f84 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23922 [000] 184694.378688: cycles: 
	    7f6b2d0a4bcc Lorg/mozilla/javascript/IdScriptableObject;.has (/tmp/perf-23895.map)
	    7f6b2d0ca4f4 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d5b469c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d493078 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d49363c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d492f7c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

ab 23927 [003] 184694.382687: cycles: 
	ffffffff81616449 __kfree_skb ([kernel.kallsyms])
	ffffffff81672c6a tcp_clean_rtx_queue ([kernel.kallsyms])
	ffffffff816739e8 tcp_ack ([kernel.kallsyms])
	ffffffff816741b2 tcp_rcv_established ([kernel.kallsyms])
	ffffffff8167e105 tcp_v4_do_rcv ([kernel.kallsyms])
	ffffffff81680510 tcp_v4_rcv ([kernel.kallsyms])
	ffffffff8165b268 ip_local_deliver_finish ([kernel.kallsyms])
	ffffffff8165b568 ip_local_deliver ([kernel.kallsyms])
	ffffffff8165aeed ip_rcv_finish ([kernel.kallsyms])
	ffffffff8165b838 ip_rcv ([kernel.kallsyms])
	ffffffff81625056 __netif_receive_skb_core ([kernel.kallsyms])
	ffffffff81625248 __netif_receive_skb ([kernel.kallsyms])
	ffffffff81625dde process_backlog ([kernel.kallsyms])
	ffffffff81625632 net_rx_action ([kernel.kallsyms])
	ffffffff8106cc1c __do_softirq ([kernel.kallsyms])
	ffffffff8173329c do_softirq_own_stack ([kernel.kallsyms])
	ffffffff8106ce95 do_softirq ([kernel.kallsyms])
	ffffffff8106cf24 local_bh_enable ([kernel.kallsyms])
	ffffffff8165f8c8 ip_finish_output ([kernel.kallsyms])
	ffffffff81660e28 ip_output ([kernel.kallsyms])
	ffffffff81660585 ip_local_out ([kernel.kallsyms])
	ffffffff816608ea ip_queue_xmit ([kernel.kallsyms])
	ffffffff816776a9 tcp_transmit_skb ([kernel.kallsyms])
	ffffffff81677c50 tcp_write_xmit ([kernel.kallsyms])
	ffffffff8167888e __tcp_push_pending_frames ([kernel.kallsyms])
	ffffffff81669ec8 tcp_sendmsg ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cabd51d0 [unknown] ([unknown])

java 23921 [002] 184694.383958: cycles: 
	    7f6b2d0904b8 Lorg/mozilla/javascript/ScriptableObject;.getSlot (/tmp/perf-23895.map)
	    7f6b2d0c48ac Lorg/mozilla/javascript/IdScriptableObject;.get (/tmp/perf-23895.map)
	    7f6b2d0cd4fc Lorg/mozilla/javascript/ScriptRuntime;.nameOrFunction (/tmp/perf-23895.map)
	    7f6b2d583528 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d41cac0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d41d15c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d41c9c4 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [001] 184694.384113: cycles: 
	    7f6b2d4bc341 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23922 [000] 184694.394803: cycles: 
	ffffffff8167edee __tcp_v4_send_check ([kernel.kallsyms])
	ffffffff8167ee3c tcp_v4_send_check ([kernel.kallsyms])
	ffffffff816775b5 tcp_transmit_skb ([kernel.kallsyms])
	ffffffff81677c50 tcp_write_xmit ([kernel.kallsyms])
	ffffffff8167888e __tcp_push_pending_frames ([kernel.kallsyms])
	ffffffff81669ec8 tcp_sendmsg ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f6b42bfc35d [unknown] (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f6b2d2eb14b Lsun/nio/ch/FileDispatcherImpl;.write0 (/tmp/perf-23895.map)
	    7f6b2d3dee80 Lsun/nio/ch/SocketChannelImpl;.write (/tmp/perf-23895.map)
	    7f6b2d3d1e74 Lio/netty/buffer/PooledUnsafeDirectByteBuf;.getBytes (/tmp/perf-23895.map)
	    7f6b2d4d1e0c Lio/netty/channel/nio/AbstractNioByteChannel;.doWrite (/tmp/perf-23895.map)
	    7f6b2d3cac1c Lio/netty/channel/DefaultChannelPipeline$HeadHandler;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3d0224 Lio/netty/channel/ChannelOutboundHandlerAdapter;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3c40e4 Lio/netty/channel/ChannelDuplexHandler;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3c8ba8 Lorg/vertx/java/core/net/impl/VertxHandler;.channelReadComplete (/tmp/perf-23895.map)
	    7f6b2d25fb34 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelReadComplete (/tmp/perf-23895.map)
	    7f6b2d3ebe58 Lio/netty/handler/codec/ByteToMessageDecoder;.channelReadComplete (/tmp/perf-23895.map)
	    7f6b2d25fb34 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelReadComplete (/tmp/perf-23895.map)
	    7f6b2d5584c4 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

ab 23927 [003] 184694.397164: cycles: 
	ffffffff8161517e __kmalloc_reserve.isra.26 ([kernel.kallsyms])
	ffffffff81615b1e __alloc_skb ([kernel.kallsyms])
	ffffffff81669609 sk_stream_alloc_skb ([kernel.kallsyms])
	ffffffff8166a57b tcp_sendmsg ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cabe2590 [unknown] ([unknown])

java 23921 [002] 184694.398792: cycles: 
	    7f6b2d2fd3e4 Ljava/lang/reflect/Method;.invoke (/tmp/perf-23895.map)
	    7f6b2d3094bc Lorg/mozilla/javascript/MemberBox;.invoke (/tmp/perf-23895.map)
	    7f6b2d4078bc Lorg/mozilla/javascript/NativeJavaMethod;.call (/tmp/perf-23895.map)
	    7f6b2d41cecc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d57d43c Lorg/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4;._c_anonymous_1 (/tmp/perf-23895.map)
	    7f6b2d56e75c Lorg/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4;.call (/tmp/perf-23895.map)
	    7f6b2d41d234 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d41c9c4 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [001] 184694.399637: cycles: 
	    7f6b42bf6900 pthread_self (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f6b2d3deddc Lsun/nio/ch/SocketChannelImpl;.write (/tmp/perf-23895.map)
	    7f6b2d3d1e74 Lio/netty/buffer/PooledUnsafeDirectByteBuf;.getBytes (/tmp/perf-23895.map)
	    7f6b2d4d1e0c Lio/netty/channel/nio/AbstractNioByteChannel;.doWrite (/tmp/perf-23895.map)
	    7f6b2d3cac1c Lio/netty/channel/DefaultChannelPipeline$HeadHandler;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3d0224 Lio/netty/channel/ChannelOutboundHandlerAdapter;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3c40e4 Lio/netty/channel/ChannelDuplexHandler;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3c8ba8 Lorg/vertx/java/core/net/impl/VertxHandler;.channelReadComplete (/tmp/perf-23895.map)
	    7f6b2d25fb34 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelReadComplete (/tmp/perf-23895.map)
	    7f6b2d3ebe58 Lio/netty/handler/codec/ByteToMessageDecoder;.channelReadComplete (/tmp/perf-23895.map)
	    7f6b2d25fb34 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelReadComplete (/tmp/perf-23895.map)
	    7f6b2d5584c4 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23922 [000] 184694.408782: cycles: 
	    7f6b2d20a8d3 Lio/netty/handler/codec/http/DefaultHttpHeaders;.add0 (/tmp/perf-23895.map)
	    7f6b2d2fdccc Ljava/lang/reflect/Method;.invoke (/tmp/perf-23895.map)
	    7f6b2d3094bc Lorg/mozilla/javascript/MemberBox;.invoke (/tmp/perf-23895.map)
	    7f6b2d4078bc Lorg/mozilla/javascript/NativeJavaMethod;.call (/tmp/perf-23895.map)
	    7f6b2d4933ac Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d57f17c Lorg/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_2;.call (/tmp/perf-23895.map)
	    7f6b2d493714 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d492f7c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [002] 184694.412007: cycles: 
	ffffffff81728c21 _raw_spin_unlock_irqrestore ([kernel.kallsyms])
	ffffffff8109a88a try_to_wake_up ([kernel.kallsyms])
	ffffffff8109a9c2 default_wake_function ([kernel.kallsyms])
	ffffffff810aa9c8 __wake_up_common ([kernel.kallsyms])
	ffffffff810aaa13 __wake_up_locked ([kernel.kallsyms])
	ffffffff812064b8 ep_poll_callback ([kernel.kallsyms])
	ffffffff810aa9c8 __wake_up_common ([kernel.kallsyms])
	ffffffff810aaf75 __wake_up_sync_key ([kernel.kallsyms])
	ffffffff8160efca sock_def_readable ([kernel.kallsyms])
	ffffffff81671217 tcp_data_queue ([kernel.kallsyms])
	ffffffff816741f4 tcp_rcv_established ([kernel.kallsyms])
	ffffffff8167e105 tcp_v4_do_rcv ([kernel.kallsyms])
	ffffffff81680510 tcp_v4_rcv ([kernel.kallsyms])
	ffffffff8165b268 ip_local_deliver_finish ([kernel.kallsyms])
	ffffffff8165b568 ip_local_deliver ([kernel.kallsyms])
	ffffffff8165aeed ip_rcv_finish ([kernel.kallsyms])
	ffffffff8165b838 ip_rcv ([kernel.kallsyms])
	ffffffff81625056 __netif_receive_skb_core ([kernel.kallsyms])
	ffffffff81625248 __netif_receive_skb ([kernel.kallsyms])
	ffffffff81625dde process_backlog ([kernel.kallsyms])
	ffffffff81625632 net_rx_action ([kernel.kallsyms])
	ffffffff8106cc1c __do_softirq ([kernel.kallsyms])
	ffffffff8173329c do_softirq_own_stack ([kernel.kallsyms])
	ffffffff8106ce95 do_softirq ([kernel.kallsyms])
	ffffffff8106cf24 local_bh_enable ([kernel.kallsyms])
	ffffffff8165f8c8 ip_finish_output ([kernel.kallsyms])
	ffffffff81660e28 ip_output ([kernel.kallsyms])
	ffffffff81660585 ip_local_out ([kernel.kallsyms])
	ffffffff816608ea ip_queue_xmit ([kernel.kallsyms])
	ffffffff816776a9 tcp_transmit_skb ([kernel.kallsyms])
	ffffffff81677c50 tcp_write_xmit ([kernel.kallsyms])
	ffffffff8167888e __tcp_push_pending_frames ([kernel.kallsyms])
	ffffffff81669ec8 tcp_sendmsg ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f6b42bfc35d [unknown] (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f6b2d2eb14b Lsun/nio/ch/FileDispatcherImpl;.write0 (/tmp/perf-23895.map)
	    7f6b2d3dee80 Lsun/nio/ch/SocketChannelImpl;.write (/tmp/perf-23895.map)
	    7f6b2d3d1e74 Lio/netty/buffer/PooledUnsafeDirectByteBuf;.getBytes (/tmp/perf-23895.map)
	    7f6b2d4d1e0c Lio/netty/channel/nio/AbstractNioByteChannel;.doWrite (/tmp/perf-23895.map)
	    7f6b2d3cac1c Lio/netty/channel/DefaultChannelPipeline$HeadHandler;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3d0224 Lio/netty/channel/ChannelOutboundHandlerAdapter;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3c40e4 Lio/netty/channel/ChannelDuplexHandler;.flush (/tmp/perf-23895.map)
	    7f6b2d1b2534 Lio/netty/channel/DefaultChannelHandlerContext;.flush (/tmp/perf-23895.map)
	    7f6b2d3c8ba8 Lorg/vertx/java/core/net/impl/VertxHandler;.channelReadComplete (/tmp/perf-23895.map)
	    7f6b2d25fb34 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelReadComplete (/tmp/perf-23895.map)
	    7f6b2d3ebe58 Lio/netty/handler/codec/ByteToMessageDecoder;.channelReadComplete (/tmp/perf-23895.map)
	    7f6b2d25fb34 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelReadComplete (/tmp/perf-23895.map)
	    7f6b2d5584c4 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [001] 184694.413293: cycles: 
	    7f6b2d090407 Lorg/mozilla/javascript/ScriptableObject;.getSlot (/tmp/perf-23895.map)
	    7f6b2d0a4908 Lorg/mozilla/javascript/IdScriptableObject;.has (/tmp/perf-23895.map)
	    7f6b2d0ca4f4 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d5952dc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

ab 23927 [003] 184694.413444: cycles: 
	ffffffff8167b9f0 tcp_v4_md5_lookup ([kernel.kallsyms])
	ffffffff81676d54 tcp_current_mss ([kernel.kallsyms])
	ffffffff816680ac tcp_send_mss ([kernel.kallsyms])
	ffffffff81669f09 tcp_sendmsg ([kernel.kallsyms])
	ffffffff816937b4 inet_sendmsg ([kernel.kallsyms])
	ffffffff8160b55e sock_aio_write ([kernel.kallsyms])
	ffffffff811bd4aa do_sync_write ([kernel.kallsyms])
	ffffffff811bdd2d vfs_write ([kernel.kallsyms])
	ffffffff811be669 sys_write ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc479340 __write_nocancel (/lib/x86_64-linux-gnu/libpthread-2.19.so)
	    7f26cabf0e30 [unknown] ([unknown])

java 23922 [000] 184694.420880: cycles: 
	    7f6b2d0902c0 Lorg/mozilla/javascript/ScriptableObject;.getSlot (/tmp/perf-23895.map)
	    7f6b2d0ca428 Lorg/mozilla/javascript/ScriptRuntime;.setObjectProp (/tmp/perf-23895.map)
	    7f6b2d552aac Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49;.call (/tmp/perf-23895.map)
	    7f6b2d21679c Lorg/mozilla/javascript/optimizer/OptRuntime;.call2 (/tmp/perf-23895.map)
	    7f6b2d55305c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49;.call (/tmp/perf-23895.map)
	    7f6b2d21679c Lorg/mozilla/javascript/optimizer/OptRuntime;.call2 (/tmp/perf-23895.map)
	    7f6b2d5ab698 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;._c_anonymous_21 (/tmp/perf-23895.map)
	    7f6b2d493150 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d5b4be0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d493078 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d49363c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d492f7c Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23921 [002] 184694.423471: cycles: 
	    7f6b42515d08 __libc_enable_asynccancel (/lib/x86_64-linux-gnu/libc-2.19.so)
	    7f6b2d3de7f2 Lsun/nio/ch/EPollArrayWrapper;.epollWait (/tmp/perf-23895.map)
	    7f6b2d591320 Lsun/nio/ch/EPollArrayWrapper;.poll (/tmp/perf-23895.map)
	    7f6b2d5a4120 Lsun/nio/ch/EPollSelectorImpl;.doSelect (/tmp/perf-23895.map)
	    7f6b2d58a348 Lsun/nio/ch/SelectorImpl;.lockAndDoSelect (/tmp/perf-23895.map)
	    7f6b2d5c1420 Lio/netty/channel/nio/NioEventLoop;.select (/tmp/perf-23895.map)
	    7f6b2d5c2b44 Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23922 [001] 184694.425117: cycles: 
	    7f6b2d25e374 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [003] 184694.427558: cycles: 
	    7f6b2d13647c Lio/netty/handler/codec/http/HttpObjectDecoder;.findWhitespace (/tmp/perf-23895.map)
	    7f6b2d3ffb20 Lio/netty/handler/codec/http/HttpObjectDecoder;.decode (/tmp/perf-23895.map)
	    7f6b2d417fd0 Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

java 23923 [000] 184694.432846: cycles: 
	    7f6b2d40f900 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.getParamAndVarCount (/tmp/perf-23895.map)
	    7f6b2d5af318 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_21 (/tmp/perf-23895.map)
	    7f6b2d4bbfd0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d595ca0 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;._c_anonymous_3 (/tmp/perf-23895.map)
	    7f6b2d4bbef8 Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d2f4068 Lorg/mozilla/javascript/BaseFunction;.construct (/tmp/perf-23895.map)
	    7f6b2d4bc4bc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d4bbdfc Lorg/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93;.call (/tmp/perf-23895.map)
	    7f6b2d329434 Lorg/vertx/java/core/http/impl/ServerConnection;.handleMessage (/tmp/perf-23895.map)
	    7f6b2d350a70 Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler;.doMessageReceived (/tmp/perf-23895.map)
	    7f6b2d325fac Lorg/vertx/java/core/http/impl/VertxHttpHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d341798 Lorg/vertx/java/core/net/impl/VertxHandler;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d41812c Lio/netty/handler/codec/ByteToMessageDecoder;.channelRead (/tmp/perf-23895.map)
	    7f6b2d25e364 Lio/netty/channel/DefaultChannelHandlerContext;.fireChannelRead (/tmp/perf-23895.map)
	    7f6b2d558468 Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe;.read (/tmp/perf-23895.map)
	    7f6b2d55a664 Lio/netty/channel/nio/NioEventLoop;.processSelectedKey (/tmp/perf-23895.map)
	    7f6b2d58f7f0 Lio/netty/channel/nio/NioEventLoop;.processSelectedKeysOptimized (/tmp/perf-23895.map)
	    7f6b2d5c324c Lio/netty/channel/nio/NioEventLoop;.run (/tmp/perf-23895.map)
	    7f6b2cbe0c4d Interpreter (/tmp/perf-23895.map)
	    7f6b2cbe0c92 Interpreter (/tmp/perf-23895.map)
	    7f6b2cbd97a7 call_stub (/tmp/perf-23895.map)
	    7f6b41abe926 JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abee31 JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41abf2d7 JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41b56010 thread_entry(JavaThread*, Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9bdbf JavaThread::thread_main_inner() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41e9beec JavaThread::run() (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b41d4fb08 java_start(Thread*) (/usr/lib/jvm/jdk1.8.0_60_b19/jre/lib/amd64/server/libjvm.so)
	    7f6b42bf5182 start_thread (/lib/x86_64-linux-gnu/libpthread-2.19.so)

ab 23927 [002] 184694.435091: cycles: 
	ffffffff81724b08 __schedule ([kernel.kallsyms])
	ffffffff817251a9 schedule ([kernel.kallsyms])
	ffffffff817247ec schedule_hrtimeout_range_clock ([kernel.kallsyms])
	ffffffff81724843 schedule_hrtimeout_range ([kernel.kallsyms])
	ffffffff81206332 ep_poll ([kernel.kallsyms])
	ffffffff81207575 sys_epoll_wait ([kernel.kallsyms])
	ffffffff8173186d system_call_fastpath ([kernel.kallsyms])
	    7f26dc19f683 __epoll_wait_nocancel (/lib/x86_64-linux-gnu/libc-2.19.so)
	    7fffa95fe250 [unknown] ([vdso])
	   1000100000001 [unknown] ([unknown])
