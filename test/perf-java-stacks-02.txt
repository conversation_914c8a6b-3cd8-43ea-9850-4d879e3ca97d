java 19983 cycles:
ffffffff8103d0ca native_write_msr_safe ([kernel.kallsyms])
ffffffff810275f1 intel_pmu_enable_all ([kernel.kallsyms])
ffffffff81028021 intel_pmu_nhm_enable_all ([kernel.kallsyms])
ffffffff81024282 x86_pmu_enable ([kernel.kallsyms])
ffffffff8110ea8b perf_pmu_enable ([kernel.kallsyms])
ffffffff81022966 x86_pmu_commit_txn ([kernel.kallsyms])
ffffffff8110fb2a group_sched_in ([kernel.kallsyms])
ffffffff811105ea __perf_event_enable ([kernel.kallsyms])
ffffffff8110b928 remote_function ([kernel.kallsyms])
ffffffff810a20e6 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
ffffffff81030a57 smp_call_function_single_interrupt ([kernel.kallsyms])
ffffffff816647de call_function_single_interrupt ([kernel.kallsyms])
7f7241fbe6d8 arrayOopDesc::base(BasicType) const (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f7241239aec writeBytes (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/libjava.so)
7f724123176f Java_java_io_FileOutputStream_writeBytes (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/libjava.so)
7f722d119786 Ljava/io/FileOutputStream;::writeBytes (/tmp/perf-19982.map)
7f722d142778 Ljava/io/PrintStream;::print (/tmp/perf-19982.map)
7f722d12e1d4 LBusy;::main (/tmp/perf-19982.map)
7f722d0004e7 call_stub (/tmp/perf-19982.map)
7f72421d2d76 JavaCalls::call_helper(JavaValue, methodHandle, JavaCallArguments, Thread) (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f72421ed566 jni_invoke_static(JNIEnv_, JavaValue, _jobject, JNICallType, _jmethodID, JNI_ArgumentPusher, Thread) (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f72421f987a jni_CallStaticVoidMethod (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f724309ebdf JavaMain (/home/<USER>/opt/jdk1.8.0_112/lib/amd64/jli/libjli.so)
7f72432b4e9a start_thread (/lib/x86_64-linux-gnu/libpthread-2.15.so)

java 19983 cycles:
ffffffff8103d0ca native_write_msr_safe ([kernel.kallsyms])
ffffffff810275f1 intel_pmu_enable_all ([kernel.kallsyms])
ffffffff81028021 intel_pmu_nhm_enable_all ([kernel.kallsyms])
ffffffff81024282 x86_pmu_enable ([kernel.kallsyms])
ffffffff8110ea8b perf_pmu_enable ([kernel.kallsyms])
ffffffff81022966 x86_pmu_commit_txn ([kernel.kallsyms])
ffffffff8110fb2a group_sched_in ([kernel.kallsyms])
ffffffff811105ea __perf_event_enable ([kernel.kallsyms])
ffffffff8110b928 remote_function ([kernel.kallsyms])
ffffffff810a20e6 generic_smp_call_function_single_interrupt ([kernel.kallsyms])
ffffffff81030a57 smp_call_function_single_interrupt ([kernel.kallsyms])
ffffffff816647de call_function_single_interrupt ([kernel.kallsyms])
7f7241fbe6d8 arrayOopDesc::base(BasicType) const (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f7241239aec writeBytes (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/libjava.so)
7f724123176f Java_java_io_FileOutputStream_writeBytes (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/libjava.so)
7f722d119786 Ljava/io/FileOutputStream;::writeBytes (/tmp/perf-19982.map)
7f722d142778 Ljava/io/PrintStream;::print (/tmp/perf-19982.map)
7f722d12e1d4 LBusy;::main (/tmp/perf-19982.map)
7f722d0004e7 call_stub (/tmp/perf-19982.map)
7f72421d2d76 JavaCalls::call_helper(JavaValue, methodHandle, JavaCallArguments, Thread) (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f72421ed566 jni_invoke_static(JNIEnv_, JavaValue, _jobject, JNICallType, _jmethodID, JNI_ArgumentPusher, Thread) (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f72421f987a jni_CallStaticVoidMethod (/home/<USER>/opt/jdk1.8.0_112/jre/lib/amd64/server/libjvm.so)
7f724309ebdf JavaMain (/home/<USER>/opt/jdk1.8.0_112/lib/amd64/jli/libjli.so)
7f72432b4e9a start_thread (/lib/x86_64-linux-gnu/libpthread-2.15.so)

