java;_int_malloc 2
java;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub_[j];java/lang/Thread:::run_[j];Interpreter_[j];java/util/concurrent/ThreadPoolExecutor$Worker:::run_[j];java/util/concurrent/ThreadPoolExecutor:::runWorker_[j];org/apache/tomcat/util/net/AprEndpoint$SocketProcessor:::run_[j];org/apache/tomcat/util/net/AprEndpoint$SocketProcessor:::doRun_[j];org/apache/coyote/AbstractProtocol$AbstractConnectionHandler:::process_[j];org/apache/coyote/http11/AbstractHttp11Processor:::process_[j];org/apache/catalina/connector/CoyoteAdapter:::service_[j];org/apache/coyote/http11/AbstractHttp11Processor:::action_[j];org/apache/tomcat/jni/Socket:::sendbb_[j] 1
java;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub_[j];net/spy/memcached/EVCacheConnection:::run_[j];net/spy/memcached/MemcachedConnection:::handleIO_[j];net/spy/memcached/MemcachedConnection:::handleIO_[j];net/spy/memcached/MemcachedConnection:::handleReads_[j];net/spy/memcached/protocol/binary/OperationImpl:::readFromBuffer_[j];net/spy/memcached/protocol/binary/OperationImpl:::finishedPayload_[j];net/spy/memcached/protocol/binary/GetOperationImpl:::decodePayload_[j];net/spy/memcached/transcoders/TranscodeService$1:::call_[j];XXX::XXX_[j];java/util/zip/Inflater:::inflateBytes_[j];Java_java_util_zip_Inflater_inflateBytes;inflate;__memmove_ssse3_back 18
java;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub_[j];net/spy/memcached/EVCacheConnection:::run_[j];net/spy/memcached/MemcachedConnection:::handleIO_[j];net/spy/memcached/MemcachedConnection:::handleIO_[j];net/spy/memcached/MemcachedConnection:::handleReads_[j];net/spy/memcached/protocol/binary/OperationImpl:::readFromBuffer_[j];net/spy/memcached/protocol/binary/OperationImpl:::finishedPayload_[j];net/spy/memcached/protocol/binary/GetOperationImpl:::decodePayload_[j];net/spy/memcached/transcoders/TranscodeService$1:::call_[j];com/XXX::XXX_[j];java/util/zip/Inflater:::inflateBytes_[j];Java_java_util_zip_Inflater_inflateBytes;inflate;__memmove_ssse3_back 1
perf;__libc_start_main;main;run_builtin;cmd_record 40
perf;do_lookup_x 27
sleep;[unknown];memcmp 24
sleep;__execve;return_from_execve_[k];sys_execve_[k];do_execveat_common.isra.31_[k];search_binary_handler_[k];copy_user_enhanced_fast_string_[k] 1
sleep;__execve;return_from_execve_[k];sys_execve_[k];do_execveat_common.isra.31_[k];search_binary_handler_[k];load_elf_binary_[k];padzero_[k];clear_user_[k];__clear_user_[k] 2
sleep;_dl_start_user;_dl_start 9
sleep;_start 3
sleep;handle_intel 72
