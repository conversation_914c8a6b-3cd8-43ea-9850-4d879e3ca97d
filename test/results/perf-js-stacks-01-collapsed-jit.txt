node-v011;__libc_start_main;node::Start;uv_run;uv__io_poll;uv__async_io;uv__async_event;uv__work_done;node::After;v8::Function::Call;v8::internal::Execution::Call;v8::internal::Invoke;Stub:JSEntryStub_[j];Builtin:A builtin from the snapshot_[j];LazyCompile:_tickDomainCallback node.js:387_[j];LazyCompile:*Async$consumeFunctionBuffer /apps/node/webapp/node_modules/xxxxx/js/main/async.js:39_[j];RegExp:\bFoo ?Bar_[j] 1
node-v011;__libc_start_main;node::Start;uv_run;uv__io_poll;uv__async_io;uv__async_event;uv__work_done;node::After;v8::Function::Call;v8::internal::Execution::Call;v8::internal::Invoke;Stub:JSEntryStub_[j];Builtin:A builtin from the snapshot_[j];LazyCompile:_tickDomainCallback node.js:387_[j];LazyCompile:*Async$consumeFunctionBuffer /apps/node/webapp/node_modules/xxxxx/js/main/async.js:39_[j];v8::internal::Execution::Call;LazyCompile:~body_0 evalmachine.<anonymous>:1_[j];RegExp:[&<>\\]_[j] 1
