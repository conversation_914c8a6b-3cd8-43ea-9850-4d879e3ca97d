java;[perf-10939.map]_[j] 23
java;[perf-10939.map]_[j];OptoRuntime::multianewarray2_C;ObjArrayKlass::multi_allocate;TypeArrayKlass::multi_allocate;TypeArrayKlass::allocate_common;page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];cpumask_next_and_[k];find_next_bit_[k] 1
java;[perf-10939.map]_[j];OptoRuntime::multianewarray2_C;ObjArrayKlass::multi_allocate;TypeArrayKlass::multi_allocate;TypeArrayKlass::allocate_common;page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];xen_send_IPI_one_[k];xen_hypercall_event_channel_op_[k] 5
java;[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k] 1
java;[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];_raw_spin_unlock_irqrestore_[k] 1
java;[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];xen_send_IPI_one_[k];notify_remote_via_irq_[k] 1
java;[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];xen_send_IPI_one_[k];notify_remote_via_irq_[k];evtchn_from_irq_[k];irq_get_irq_data_[k];irq_to_desc_[k];radix_tree_lookup_element_[k] 1
java;[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];xen_send_IPI_one_[k];xen_hypercall_event_channel_op_[k] 23
java;[unknown];[perf-10939.map]_[j] 2
java;[unknown];[perf-10939.map]_[j];[perf-10939.map]_[j] 1
java;[unknown];[perf-10939.map]_[j];[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];xen_send_IPI_one_[k];xen_hypercall_event_channel_op_[k] 9
java;[unknown];[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k] 2
java;[unknown];[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];__xen_send_IPI_mask_[k];xen_send_IPI_one_[k];xen_hypercall_event_channel_op_[k] 53
java;[unknown];[perf-10939.map]_[j];page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];do_numa_page_[k];migrate_misplaced_page_[k];migrate_pages_[k];try_to_unmap_[k];try_to_unmap_anon_[k];try_to_unmap_one_[k];ptep_clear_flush_[k];flush_tlb_page_[k];native_flush_tlb_others_[k];smp_call_function_many_[k];xen_smp_send_call_function_ipi_[k];find_next_bit_[k] 1
java;[unknown];[perf-10939.map]_[j];retint_signal_[k];do_notify_resume_[k];task_work_run_[k];task_numa_work_[k];change_prot_numa_[k];change_protection_[k];change_protection_range_[k] 1
swapper;start_secondary_[k];cpu_startup_entry_[k];arch_cpu_idle_[k];default_idle_[k];native_safe_halt_[k] 72
swapper;x86_64_start_kernel_[k];x86_64_start_reservations_[k];start_kernel_[k];rest_init_[k];cpu_startup_entry_[k];arch_cpu_idle_[k];default_idle_[k];native_safe_halt_[k] 2
