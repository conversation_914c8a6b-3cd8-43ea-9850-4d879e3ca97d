java-?/3244;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3245;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 1
java-?/3245;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3246;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 1
java-?/3247;start_thread;java_start;GCTaskThread::run;ScavengeRootsTask::do_it;ClassLoaderDataGraph::oops_do;ClassLoaderData::oops_do;PSScavengeKlassClosure::do_klass 1
java-?/3248;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3249;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3250;start_thread;java_start;GCTaskThread::run;StealTask::do_it;PSPromotionManager::drain_stacks_depth;oopDesc* PSPromotionManager::copy_to_survivor_space<false>;InstanceKlass::oop_push_contents 1
java-?/3251;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3252;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3253;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 1
java-?/3254;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 1
java-?/3255;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 1
java-?/3256;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 1
java-?/3257;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;PSIsAliveClosure::do_object_b 1
java-?/3257;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;StringTable::unlink_or_oops_do 2
java-?/3257;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;pthread_cond_signal@@GLIBC_2.3.2;system_call_fastpath;sys_futex;do_futex;futex_wake_op 1
java-?/3278;read;check_events;hypercall_page 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/buffer/AbstractByteBufAllocator:.directBuffer 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/buffer/AbstractReferenceCountedByteBuf:.release 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Ljava/util/concurrent/ConcurrentHashMap:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/mozilla/javascript/Context:.getWrapFactory 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived 3
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/ScriptableObject:.getParentScope 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/WrapFactory:.wrap;Ljava/util/HashMap:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/WrapFactory:.wrapAsJavaObject 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/WrapFactory:.wrapAsJavaObject;Ljava/util/HashMap:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp;Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp;vtable chunks 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.name;Lorg/mozilla/javascript/IdScriptableObject:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction;Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.has 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.has;Lorg/mozilla/javascript/ScriptableObject:.getSlot 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.setAttributes 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/MemberBox:.invoke 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/WrapFactory:.wrap 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.findFunction 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaObject:.get 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.get;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.setAttributes 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp;Lorg/mozilla/javascript/ScriptableObject$Slot:.getValue 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis;Lorg/mozilla/javascript/NativeJavaObject:.get;Ljava/util/HashMap:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.get 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/IdScriptableObject:.has 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.setAttributes 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.getObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.get;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction;Lorg/mozilla/javascript/IdScriptableObject:.get;Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 3
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;vtable chunks 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/NativeFunction:.initScriptFunction 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 6
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptableObject:.getParentScope 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.newObject;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;jint_disjoint_arraycopy 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has 4
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has;Lorg/mozilla/javascript/ScriptableObject:.getSlot 5
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 6
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptableObject:.getPrototype 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptableObject:.getParentScope 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/ScriptRuntime:.name;Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction;vtable chunks 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.has;Lorg/mozilla/javascript/ScriptableObject:.getSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/IdScriptableObject:.setAttributes 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.createFunctionActivation;Lorg/mozilla/javascript/TopLevel:.getBuiltinPrototype 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/optimizer/OptRuntime:.call2;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.setObjectProp;Lorg/mozilla/javascript/IdScriptableObject:.put;Lorg/mozilla/javascript/ScriptableObject:.getSlot;Lorg/mozilla/javascript/ScriptableObject:.createSlot 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/ScriptRuntime:.indexFromString 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/ScriptRuntime:.setObjectElem;Lorg/mozilla/javascript/ScriptRuntime:.indexFromString 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lio/netty/handler/codec/http/DefaultHttpHeaders:.set 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke 3
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Lio/netty/buffer/AbstractByteBuf:.writeBytes 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Lio/netty/handler/codec/http/HttpObjectEncoder:.encode;Lio/netty/buffer/AbstractByteBuf:.writeBytes 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Lio/netty/handler/codec/http/HttpObjectEncoder:.encode;Lio/netty/buffer/AbstractByteBufAllocator:.directBuffer 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Lio/netty/handler/codec/http/HttpObjectEncoder:.encode;Lio/netty/buffer/AbstractByteBufAllocator:.directBuffer;Lio/netty/util/concurrent/FastThreadLocal:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Lio/netty/handler/codec/http/HttpObjectEncoder:.encode;Ljava/util/ArrayList:.add 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Lio/netty/util/internal/RecyclableArrayList:.newInstance;Lio/netty/util/concurrent/FastThreadLocal:.get 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;Ljava/util/ArrayList:.ensureExplicitCapacity 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lio/netty/handler/codec/MessageToMessageEncoder:.write;vtable chunks 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/channel/AbstractChannelHandlerContext:.write;Lorg/vertx/java/core/http/impl/VertxHttpHandler:.write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/handler/codec/http/DefaultHttpHeaders:.add0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lio/netty/handler/codec/http/DefaultHttpHeaders:.set 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/NativeJavaMethod:.call;Lorg/mozilla/javascript/MemberBox:.invoke;Lsun/reflect/DelegatingMethodAccessorImpl:.invoke;Lsun/nio/cs/UTF_8$Encoder:.<init>;jbyte_disjoint_arraycopy 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lorg/vertx/java/core/net/impl/VertxHandler:.channelRead;Lorg/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call;Lorg/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0;Lorg/mozilla/javascript/ScriptRuntime:.name;Lorg/mozilla/javascript/ScriptRuntime:.nameOrFunction;Lorg/mozilla/javascript/IdScriptableObject:.get;Lorg/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0;Lio/netty/util/internal/AppendableCharSequence:.append 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpHeaders:.isTransferEncodingChunked 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;Lio/netty/buffer/AbstractByteBuf:.forEachByteAsc0 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;Lio/netty/handler/codec/http/HttpHeaders:.hash 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;Lio/netty/handler/codec/http/HttpObjectDecoder:.splitHeader 5
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelRead;Lio/netty/handler/codec/ByteToMessageDecoder:.channelRead;Lio/netty/handler/codec/http/HttpObjectDecoder:.decode;Lio/netty/handler/codec/http/HttpObjectDecoder:.readHeaders;Ljava/util/Arrays:.fill 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/AbstractReferenceCountedByteBuf:.release 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lio/netty/buffer/PooledByteBuf:.internalNioBuffer 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/NativeThread:.current 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;  3
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;Java_sun_nio_ch_FileDispatcherImpl_write0 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;sys_write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;fget_light 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;__srcu_read_lock 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;__tcp_push_pending_frames 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;ktime_get_real 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;skb_clone 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_set_skb_tso_segs 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_hard_start_xmit 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_pick_tx 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;dev_queue_xmit_nit 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;loopback_xmit 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;netif_rx.part.82;xen_restore_fl_direct 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;netif_rx.part.82;xen_restore_fl_direct_end 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;dma_issue_pending_all 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;__inet_lookup_established 3
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_event_data_recv 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;sock_def_readable;__wake_up_sync_key;check_events;hypercall_page 19
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack 3
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;bictcp_acked 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;ktime_get_real;getnstimeofday 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;ktime_get_real;getnstimeofday;xen_clocksource_get_cycles;xen_clocksource_read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;tcp_rtt_estimator 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;tcp_valid_rtt_meas;tcp_rtt_estimator 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver_finish 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;rcu_bh_qs 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;netif_skb_features 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ktime_get_real;getnstimeofday;xen_clocksource_get_cycles;pvclock_clocksource_read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ktime_get_real;getnstimeofday;xen_clocksource_get_cycles;xen_clocksource_read;pvclock_clocksource_read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ktime_get_real;xen_clocksource_get_cycles 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;skb_dst_set_noref 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;lock_sock_nested;_raw_spin_lock_bh;local_bh_disable 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_node_track_caller 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_node_track_caller;arch_local_irq_save 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__phys_addr 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;get_slab 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;kmem_cache_alloc_node 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;ksize 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;ipv4_mtu 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;tcp_established_options 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_xmit_size_goal 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_xmit_size_goal 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;fsnotify 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;fsnotify;__srcu_read_lock 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;rw_verify_area 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;rw_verify_area;apparmor_file_permission 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;rw_verify_area;security_file_permission;apparmor_file_permission;common_file_perm 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/FileDispatcherImpl:.write0;write;system_call_fastpath;sys_write;vfs_write;sock_aio_write 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.write;Lsun/nio/ch/SocketChannelImpl:.writerCleanup 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes;Lsun/nio/ch/SocketChannelImpl:.writerCleanup 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelDuplexHandler:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/ChannelOutboundHandlerAdapter:.flush;Lio/netty/channel/AbstractChannelHandlerContext:.flush;Lio/netty/channel/DefaultChannelPipeline$HeadContext:.flush;Lio/netty/channel/AbstractChannel$AbstractUnsafe:.flush0;Lio/netty/channel/nio/AbstractNioByteChannel:.doWrite;Lio/netty/util/Recycler:.recycle 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete;Lio/netty/channel/ChannelDuplexHandler:.flush 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete;Lorg/vertx/java/core/net/impl/VertxHandler:.channelReadComplete 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Ljava/nio/channels/spi/AbstractInterruptibleChannel:.end 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read 2
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;sys_read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;do_sync_read 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;__kfree_skb 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_rcv_space_adjust 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;__kfree_skb;skb_release_data 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;__kfree_skb;skb_release_head_state;dst_release 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;_raw_spin_lock_bh 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;skb_copy_datagram_iovec 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;skb_copy_datagram_iovec;copy_user_enhanced_fast_string 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;__tcp_select_window 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes;Lsun/nio/ch/SocketChannelImpl:.read;Lsun/nio/ch/FileDispatcherImpl:.read0;read;system_call_fastpath;sys_read;vfs_read;rw_verify_area 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read;Lio/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete 1
java-?/3278;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub;Interpreter;Interpreter;Lio/netty/channel/nio/NioEventLoop:.run;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeys;Lio/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized;Lio/netty/channel/nio/NioEventLoop:.processSelectedKey;Lio/netty/channel/socket/nio/NioSocketChannel:.doReadBytes 1
java-?/3278;write;check_events;hypercall_page 3
