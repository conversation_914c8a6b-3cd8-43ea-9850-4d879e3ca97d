#!/bin/bash

# 测试pprof是否正常工作的脚本

echo "=== 测试pprof服务 ==="

# 从配置文件读取端口
HTTP_PORT=$(grep "http_port:" cmd/config.yml | awk '{print $2}')
if [ -z "$HTTP_PORT" ]; then
    HTTP_PORT=8080
fi

echo "使用端口: $HTTP_PORT"
echo "pprof地址: http://localhost:$HTTP_PORT/debug/pprof/"

# 等待服务启动
echo "等待服务启动..."
sleep 3

# 测试pprof端点
echo "=== 测试pprof端点 ==="

endpoints=(
    "/debug/pprof/"
    "/debug/pprof/heap"
    "/debug/pprof/goroutine"
    "/debug/pprof/profile"
    "/debug/pprof/cmdline"
    "/debug/pprof/symbol"
    "/debug/pprof/trace"
)

for endpoint in "${endpoints[@]}"; do
    echo -n "测试 $endpoint ... "
    if curl -s -f "http://localhost:$HTTP_PORT$endpoint" > /dev/null; then
        echo "✓ 成功"
    else
        echo "✗ 失败"
    fi
done

echo ""
echo "=== 常用pprof命令 ==="
echo "1. 查看堆内存: go tool pprof http://localhost:$HTTP_PORT/debug/pprof/heap"
echo "2. 查看CPU性能: go tool pprof http://localhost:$HTTP_PORT/debug/pprof/profile"
echo "3. 查看goroutine: go tool pprof http://localhost:$HTTP_PORT/debug/pprof/goroutine"
echo "4. 生成火焰图: go tool pprof -http=:8081 http://localhost:$HTTP_PORT/debug/pprof/profile"
echo "5. Web界面: 直接访问 http://localhost:$HTTP_PORT/debug/pprof/"
